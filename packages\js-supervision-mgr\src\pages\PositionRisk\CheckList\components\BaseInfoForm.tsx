import React from 'react';
import { DynamicForm, DynamicFormItem, FormOpenType, SectionTitle } from 'jishan-components';
import { FormInstance } from 'antd';
import { defaultRadioOptions } from '@/utils/constants';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { getPositionList } from '@/services/positionRisk/positionList';
import { getGradingList } from '@/services/riskLevelMgr/gradingList';
import { getPersonList } from '@/services/companyInfo/workerMgr';

export const serviceWrapper = (enterpriseName: string, service: any) => {
  return () => service({ enterpriseName });
};

export const optionsWrapper = (name: string) => {
  return () =>
    fetchSelectOptions(serviceWrapper(name, getPositionList), 'positionName', 'positionName');
};

export const pointOptionsWrapper = (name: string) => {
  return () => fetchSelectOptions(serviceWrapper(name, getGradingList), 'riskName', 'riskCode');
};

export const personOptionsWrapper = (name: string) => {
  return () => fetchSelectOptions(serviceWrapper(name, getPersonList), 'personName', 'personName');
};

// TODO 岗位名称应该根据具体的企业进行查询(或可以要求后端进行过滤)
export const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['岗位名称', 'positionName', 'Select', true],
    optionsServices: optionsWrapper(''),
  },
  {
    quickItemParams: ['风险点', 'riskPoints', 'Select', true],
    optionsServices: pointOptionsWrapper(''),
  },
  {
    quickItemParams: ['排查人', 'inspectorId', 'Select', true],
    optionsServices: personOptionsWrapper(''),
  },
  { quickItemParams: ['是否启用', 'isActive', 'Radio', true], options: defaultRadioOptions },
  { quickItemParams: ['排查方法', 'checkMethod', 'Input', true] },
];

export const planFormItems: DynamicFormItem[] = [
  { quickItemParams: ['起止日期', 'dateRange', 'DatePicker', true] },
  { quickItemParams: ['执行周期', 'executionCycle', 'Select', true] },
  { quickItemParams: ['按日', 'dayType', 'Select', true] },
  { quickItemParams: ['执行时段', 'timeRangeType', 'Select', true] },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <SectionTitle title="基本信息" />
      <DynamicForm items={formItems} openType={openType} form={form} />
      <SectionTitle title="排查计划" />
      <DynamicForm items={planFormItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
