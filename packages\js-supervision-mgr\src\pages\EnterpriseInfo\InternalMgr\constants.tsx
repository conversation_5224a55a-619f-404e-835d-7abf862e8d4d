import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '内部信息';

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    span: 5,
    extraProps: {
      wrapperCol: { span: 24 },
    },
    component: <SearchInput placeholder="请输入企业名称" style={{ width: '100%' }} />,
  },
  {
    name: 'personName',
    label: '人员姓名',
    span: 5,
    extraProps: {
      wrapperCol: { span: 24 },
    },
    component: <SearchInput placeholder="请输入统一社会信用代码" style={{ width: '100%' }} />,
  },
  {
    name: 'phone',
    label: '电话号码',
    span: 5,
    extraProps: {
      wrapperCol: { span: 24 },
    },
    component: <SearchInput placeholder="请输入电话号码" style={{ width: '100%' }} />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true, width: 200 },
  { title: '人员姓名', dataIndex: 'personName', width: 120 },
  { title: '职务', dataIndex: 'position', width: 120 },
  { title: '电话', dataIndex: 'phone', width: 150 },
];
