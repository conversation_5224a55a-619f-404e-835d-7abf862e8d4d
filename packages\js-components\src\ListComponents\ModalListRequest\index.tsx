import React from 'react';
import { Button, Modal } from 'antd'; // 假设你在用 antd，也可以换成你自己的 Modal 组件
import ListRequest, { ListRequestProps } from '../ListRequest';
import { getCompName } from '../../utils/commonFunction';
import './index.less';

export interface ModalListRequestProps extends ListRequestProps {
  visible?: boolean; // 控制弹窗显示
  onCancel?: () => void; // 关闭弹窗回调
  onConfirm?: () => void;
  title?: React.ReactNode; // 弹窗标题
  width?: number | string; // 弹窗宽度
}

const ModalListRequest: React.FC<ModalListRequestProps> = ({
  visible,
  onCancel,
  onConfirm,
  title = '列表',
  width = 600,
  ...listRequestProps
}) => {
  return (
    <Modal
      open={visible}
      onCancel={onCancel}
      title={title}
      width={width}
      footer={null}
      destroyOnHidden
    >
      <div className={getCompName('modal-list')}>
        <ListRequest {...listRequestProps} />
        <div className="btn-container">
          <Button type="primary" onClick={() => onConfirm?.()}>
            确认
          </Button>
          <Button onClick={() => onCancel?.()}>取消</Button>
        </div>
      </div>
    </Modal>
  );
};

export default ModalListRequest;
