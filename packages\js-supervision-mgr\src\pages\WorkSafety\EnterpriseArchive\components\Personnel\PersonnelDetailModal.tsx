import React from 'react';
import { Modal, Descriptions, Row, Col } from 'antd';

interface PersonnelDetailModalProps {
  visible: boolean;
  onClose: () => void;
  data: any;
}

const PersonnelDetailModal: React.FC<PersonnelDetailModalProps> = ({
  visible,
  onClose,
  data,
}) => {
  if (!data) return null;

  // 判断证书是否有效
  const isValid = data.status === '有效';

  return (
    <Modal
      title={`${data.name} - 人员详情`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Row gutter={16}>
        <Col span={12}>
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="姓名">{data.name}</Descriptions.Item>
            <Descriptions.Item label="身份证号">{data.idNumber}</Descriptions.Item>
            <Descriptions.Item label="证书类别">{data.certificateType}</Descriptions.Item>
            <Descriptions.Item label="准操作项目">{data.operationProject}</Descriptions.Item>
            <Descriptions.Item label="证书状态">
              <span style={{ color: isValid ? '#52c41a' : '#ff4d4f' }}>
                {data.status}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="工作单位">{data.workUnit}</Descriptions.Item>
          </Descriptions>
        </Col>
        <Col span={12}>
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="证书编号">{data.certificateNumber}</Descriptions.Item>
            <Descriptions.Item label="发证日期">{data.issueDate}</Descriptions.Item>
            <Descriptions.Item label="有效期自">{data.validFrom}</Descriptions.Item>
            <Descriptions.Item label="有效期至">
              <span style={{ color: isValid ? '#52c41a' : '#ff4d4f' }}>
                {data.validTo}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="发证机关">{data.issuingAuthority}</Descriptions.Item>
            <Descriptions.Item label="人员类型">
              {data.personnelType === 'internal' ? '企业内部员工' : '外包公司员工'}
            </Descriptions.Item>
          </Descriptions>
        </Col>
      </Row>
    </Modal>
  );
};

export default PersonnelDetailModal;
