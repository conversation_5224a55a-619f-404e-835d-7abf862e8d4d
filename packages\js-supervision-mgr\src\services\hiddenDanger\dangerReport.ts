import { request } from '@/utils/net';

export async function saveReport(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/hiddendanger/hiddenreport/saveReportInfo`, params);
}

export async function getReportInfo(id: number) {
  return request.get(`${SPPREFIX}/hiddendanger/hiddenreport/reportInfoDetail`, {
    id,
  });
}

export async function deleteReportInfo(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/hiddendanger/hiddenreport/removeReportInfo`, params);
}
