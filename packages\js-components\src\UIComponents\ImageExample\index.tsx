import React, { useState } from 'react';
import { Image } from 'antd';
import { getCompName } from '../../utils/commonFunction';
import { ZoomInOutlined } from '@ant-design/icons';
import './index.less';

interface ExampleImageProps {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  containerStyle?: React.CSSProperties;
  imageStyle?: React.CSSProperties;
}

const ImageExample: React.FC<ExampleImageProps> = ({
  src,
  alt = '示例图',
  width = 80,
  height = 60,
  containerStyle,
  imageStyle,
}) => {
  const [visible, setVisible] = useState(false);

  const handlePreview = () => {
    setVisible(true);
  };

  return (
    <div style={{ width: width + 24, ...containerStyle }} className={getCompName('image-example')}>
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        style={imageStyle}
        preview={{
          visible,
          onVisibleChange: value => setVisible(value),
        }}
      />
      <span className="image-example-preview" onClick={handlePreview} style={{ cursor: 'pointer' }}>
        示例图 <ZoomInOutlined />
      </span>
    </div>
  );
};

export default ImageExample;
