import { Table as AntTable, Pagination, Tooltip } from 'antd';
import { ColumnType as AntdColumnType } from 'antd/es/table';
import React, { useState, useEffect } from 'react';

interface ColumnType<RecordType> extends AntdColumnType<RecordType> {
  // 在这里添加你的自定义属性
  noTooltip?: boolean;
}

export type { ColumnType };

interface RequestTableProps {
  columns: any[];
  dataSource: any[];
  customTable?: React.ReactNode;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number) => void;
  };
  tableLoading?: boolean;
  tableClass?: string;
  setPageInfo: (i: number, s: number) => void;
  scroll?: { x?: number; y?: number };
  showSizeChanger?: boolean;
  scrollYDelta?: number;
  bordered?: boolean; // 是否带边框
  rowSelection?: boolean; // 是否支持行选择
  onRowSelect?: (selectedRowKeys: React.Key[], selectedRows: any[]) => void; // 行选择回调
  rowSelectType?: 'checkbox' | 'radio';
  rowKey?: string;
  footerComp?: any;
  rowClassName?: (record: any, index: number) => string;
}

const RequestTable: React.FC<RequestTableProps> = ({
  columns,
  dataSource,
  customTable,
  tableLoading = false,
  pagination,
  tableClass,
  scroll,
  setPageInfo,
  scrollYDelta = 0,
  showSizeChanger = true,
  bordered = true,
  rowSelection = false,
  rowSelectType = 'checkbox',
  onRowSelect,
  rowClassName,
  rowKey,
  footerComp,
}) => {
  const [scrollY, setScrollY] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    setSelectedRowKeys([]);
  }, [dataSource]);

  useEffect(() => {
    const resize = () => {
      const bodyHeight = document.body.offsetHeight;
      const height = bodyHeight - 332;
      setScrollY(height);
    };
    resize();
    window.addEventListener('resize', resize);
    return () => {
      window.removeEventListener('resize', resize);
    };
  }, []);

  // 处理 columns，默认启用 ellipsis，并添加 Tooltip
  const processedColumns = columns.map(col => {
    const newCol = { ...col };
    if (newCol.ellipsis === undefined) newCol.ellipsis = true;

    // 检查 noTooltip 配置
    if (!newCol.noTooltip) {
      if (!newCol.render) {
        newCol.render = (text: any) => (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      } else {
        const originalRender = newCol.render;
        newCol.render = (text: any, record: any, index: number) => {
          const { renderTooltip } = newCol;
          const tooltip = renderTooltip ? renderTooltip(text, record, index) : text;
          return <Tooltip title={tooltip}>{originalRender(text, record, index)}</Tooltip>;
        };
      }
    }

    return newCol;
  });

  if (customTable) {
    // 使用 React.cloneElement 传递 props
    return React.isValidElement(customTable) ? (
      // @ts-ignore
      React.cloneElement(customTable, { columns, dataSource, tableLoading, pagination })
    ) : (
      <>{customTable}</>
    );
  }

  const handleRowSelectionChange = (selectedKeys: React.Key[], selectedRows: any[]) => {
    setSelectedRowKeys(selectedKeys);
    if (onRowSelect) {
      onRowSelect(selectedKeys, selectedRows);
    }
  };

  return (
    <div className="table-wrap">
      <AntTable
        columns={processedColumns}
        dataSource={dataSource}
        loading={tableLoading}
        pagination={false} // 添加分页支持
        className={tableClass}
        scroll={{ x: scroll?.x, y: scroll?.y ?? scrollY - scrollYDelta }}
        bordered={bordered} // 是否带边框
        rowClassName={rowClassName}
        rowKey={record => {
          if (rowKey) return record[rowKey];
          return record.id;
        }}
        rowSelection={
          rowSelection
            ? { selectedRowKeys, onChange: handleRowSelectionChange, type: rowSelectType }
            : undefined
        } // 行选择配置
      />
      <div className="pagination-wrap">
        {footerComp ? <div style={{ position: 'absolute', left: '50px' }}>{footerComp}</div> : ''}
        <Pagination
          current={pagination?.current}
          pageSize={pagination?.pageSize}
          total={pagination?.total}
          showTotal={total => `共 ${total} 条`}
          showSizeChanger={showSizeChanger}
          onChange={(pageIndex, pageSize) => {
            setPageInfo(pageIndex, pageSize);
          }}
        />
      </div>
    </div>
  );
};

export default RequestTable;
