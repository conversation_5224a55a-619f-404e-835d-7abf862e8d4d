import { request } from '@/utils/net';

export async function saveChemicals(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/hazardousChemicals/saveChemicals`, params);
}

export async function getChemicalsDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/hazardousChemicals/chemicalsDetail`, {
    id,
  });
}

export async function deleteChemicals(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/hazardousChemicals/removeChemicals`, params);
}
