import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm, { imgNames, fileNames } from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import { deletePersonInfo, getInternalDetail, saveInternalInfo } from '@/services/internalInfo';
import FilePreview from '@/components/FilePreview';
import { getFileNameFromUrl } from '@/utils/commonFunction';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

let fileUrl = '';

const InternalMgr: React.FC = () => {
  const [previewOpen, setPreviewOpen] = useState(false);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHand<PERSON>,
    detailHandler,
    selectedRowsDel,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deletePersonInfo);
  };

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getInternalDetail);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getInternalDetail);
  };

  const onPreview = (url: any) => {
    fileUrl = url;
    setPreviewOpen(true);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '任命文件附件',
      dataIndex: 'appointmentFile',
      width: 180,
      render: (text: string) => (
        <CommonLinkButton onClick={() => onPreview(text)}>
          {getFileNameFromUrl(text)}
        </CommonLinkButton>
      ),
    },
    {
      title: '资质证件',
      dataIndex: 'qualificationFiles',
      width: 180,
      render: (text: string) => (
        <CommonLinkButton
          onClick={() => {
            fileUrl = text;
            setPreviewOpen(true);
          }}
        >
          {getFileNameFromUrl(text)}
        </CommonLinkButton>
      ),
    },
    {
      title: '操作',
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveInternalInfo)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        imageFieldNames: imgNames,
        normalFileNames: fileNames,
        fileStringFormat: true,
        imgStringFormat: true,
      }}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/internal/internalList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
      />
      <FilePreview path={fileUrl} open={previewOpen} onClose={() => setPreviewOpen(false)} />
    </CommonPage>
  );
};

export default InternalMgr;
