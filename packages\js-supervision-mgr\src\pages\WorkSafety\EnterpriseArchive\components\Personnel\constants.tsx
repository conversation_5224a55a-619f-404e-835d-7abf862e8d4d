import React from 'react';
import { SearchInput } from 'jishan-components';
import { Select } from 'antd';

// 表格列定义
export const tableColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    width: 120,
    ellipsis: true,
  },
  {
    title: '身份证号',
    dataIndex: 'idNumber',
    width: 180,
    ellipsis: true,
  },
  {
    title: '证书类别',
    dataIndex: 'certificateType',
    width: 200,
    ellipsis: true,
    render: (text: string) => {
      if (text === '危险化学品安全作业') {
        return <span style={{ color: '#ff4d4f' }}>{text}</span>;
      }
      return text;
    },
  },
  {
    title: '准操作项目',
    dataIndex: 'operationProject',
    width: 150,
    ellipsis: true,
  },
  {
    title: '证书状态',
    dataIndex: 'status',
    width: 100,
    render: (text: string) => {
      const color = text === '有效' ? '#52c41a' : '#ff4d4f';
      return <span style={{ color }}>{text}</span>;
    },
  },
];

// 筛选项定义
export const filterItems = [
  {
    name: 'name',
    label: '人员姓名',
    component: <SearchInput placeholder="请输入人员姓名" />,
    span: 6,
  },
  {
    name: 'idNumber',
    label: '身份证号',
    component: <SearchInput placeholder="请输入身份证号" />,
    span: 6,
  },
  {
    name: 'certificateType',
    label: '证书类别',
    component: (
      <Select
        placeholder="请选择证书类别"
        options={[
          { label: '危险化学品安全作业', value: '危险化学品安全作业' },
          { label: '电工作业', value: '电工作业' },
          { label: '焊工作业', value: '焊工作业' },
          { label: '高处作业', value: '高处作业' },
        ]}
      />
    ),
    span: 6,
  },
  {
    name: 'operationProject',
    label: '操作项目',
    component: (
      <Select
        placeholder="——请选择——"
        options={[
          { label: '动火作业', value: '动火作业' },
          { label: '临时用电作业', value: '临时用电作业' },
          { label: '受限空间作业', value: '受限空间作业' },
          { label: '高处作业', value: '高处作业' },
        ]}
      />
    ),
    span: 6,
  },
];

// 证书状态选项
export const certificateStatusOptions = [
  { label: '有效', value: 'valid' },
  { label: '无效', value: 'invalid' },
];

// 人员分类选项
export const personnelTypeOptions = [
  { label: '企业内部员工', value: 'internal' },
  { label: '外包公司员工', value: 'external' },
];

// 模拟数据
export const mockData = {
  data: [
    {
      id: 1,
      name: '张丽',
      idNumber: '210105199908070310',
      certificateType: '危险化学品安全作业',
      operationProject: '动火作业',
      status: '无效',
      personnelType: 'internal',
      certificateNumber: 'T350100202100001',
      issueDate: '2021-03-15',
      validFrom: '2021-03-15',
      validTo: '2024-03-14',
      issuingAuthority: '山西省应急管理厅',
      workUnit: '稷山县永东化工股份有限公司',
    },
    {
      id: 2,
      name: '李小明',
      idNumber: '315107198807032199',
      certificateType: '电工作业',
      operationProject: '临时用电作业',
      status: '有效',
      personnelType: 'internal',
      certificateNumber: 'T350100202100002',
      issueDate: '2022-06-20',
      validFrom: '2022-06-20',
      validTo: '2025-06-19',
      issuingAuthority: '山西省应急管理厅',
      workUnit: '稷山县永东化工股份有限公司',
    },
    {
      id: 3,
      name: '赵冬',
      idNumber: '316108197503020766',
      certificateType: '危险化学品安全作业',
      operationProject: '动火作业',
      status: '有效',
      personnelType: 'external',
      certificateNumber: 'T350100202100003',
      issueDate: '2023-01-10',
      validFrom: '2023-01-10',
      validTo: '2026-01-09',
      issuingAuthority: '山西省应急管理厅',
      workUnit: '山西安全技术服务有限公司',
    },
  ],
  total: 3,
};

// 统计信息计算
export const getStatistics = (data: any[]) => {
  const total = data.length;
  const valid = data.filter(item => item.status === '有效').length;
  const invalid = data.filter(item => item.status === '无效').length;
  const expired = data.filter(item => item.status === '过期').length;

  return {
    total,
    valid,
    invalid,
    expired,
  };
};
