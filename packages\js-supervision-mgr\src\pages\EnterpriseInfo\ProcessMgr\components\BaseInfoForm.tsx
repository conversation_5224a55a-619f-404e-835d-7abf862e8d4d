import React from 'react';
import { FormInstance } from 'antd';
import imagesImg from '@/assets/images/common/image.png';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { companyModalSelectParams, defaultRadioOptions } from '@/utils/constants';

const { nameLengthRules, codeRules, codeLengthRules, textAreaLengthRules } = RULES;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    rules: [nameLengthRules],
    modalSelectInfo: companyModalSelectParams,
  },
  {
    quickItemParams: ['重点监管危化工工艺名称', 'processName', 'Input', true],
    rules: [nameLengthRules],
  },
  { quickItemParams: ['工艺位置', 'processLocation', 'Input', false] },
  { quickItemParams: ['作用', 'processRole', 'Input', false] },
  {
    quickItemParams: ['工艺编码', 'processCode', 'Input', false],
    rules: [codeRules, codeLengthRules],
  },
  { quickItemParams: ['自动控制措施', 'autoControlMeasures', 'Input', false] },
  { quickItemParams: ['产品', 'product', 'Input', false] },
  { quickItemParams: ['产品物质形态', 'productState', 'Input', false] },
  { quickItemParams: ['副产品', 'byproduct', 'Input', false] },
  { quickItemParams: ['副产品物质形态', 'byproductState', 'Input', false] },
  {
    quickItemParams: ['是否重点监管化工工艺', 'isKeyProcess', 'Radio', false],
    options: defaultRadioOptions,
  },
  {
    quickItemParams: ['是否满足国家规定', 'isMeetRegulation', 'Radio', false],
    options: defaultRadioOptions,
  },
  { quickItemParams: ['岗位操作人数', 'operatorCount', 'IntegerInput', true] },
  { quickItemParams: ['技术来源', 'techSource', 'Input', false] },
  { quickItemParams: ['设计单位', 'designUnit', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['设计单位资质', 'designQualification', 'Input', true] },
  {
    quickItemParams: ['资质附件', 'qualificationAttachment', 'FileUpload', true],
    uploadFileInfo: { tip: '附件大小不允许超过50M', maxFileSize: 50 },
  },
  {
    quickItemParams: ['重点监控工艺参数', 'monitoredParameters', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  { quickItemParams: ['反应类型', 'reactionType', 'Input', false] },
  {
    quickItemParams: ['工艺流程图', 'processFlowDiagram', 'ImgUpload', false],
    imgInfo: { imagesImg },
  },
  {
    quickItemParams: ['安全控制的基本要求', 'safetyControlRequirements', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['工艺系统简况', 'processSystemSummary', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  { name: 'enterpriseCode', type: 'hideItem', relatedValue: 'enterpriseName.enterpriseCode' },
];

export const fileNames = ['qualificationAttachment'];

export const imgNames = ['processFlowDiagram'];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
