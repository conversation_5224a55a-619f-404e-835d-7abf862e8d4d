import React from 'react';
import {
  CustomButton,
  DynamicFormItem,
  FixedWidthRangePicker,
  SearchInput,
  RULES,
} from 'jishan-components';
import { baseButtonStyle, companyModalSelectParams, defaultRadioOptions } from '@/utils/constants';
import { defaultOptionValueWrapper, dictConfigWrapper } from '@/utils/commonFunction';
import dayjs from 'dayjs';
import { codeLengthRules, codeRules } from 'jishan-components/dist/utils/form';
import { setSignTimeWrapper, getOpinionItems } from './utils';

export const HomeTitle = '特殊作业票证管理';

export const colParams = {
  span: 16,
  labelCol: { span: 4 },
};

export const defaultDateFormat = { format: 'YYYY-MM-DD HH:mm:ss' };

export const centerLabelStyles: any = {
  whiteSpace: 'normal',
  wordBreak: 'break-all',
  minHeight: 66,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

export const getLocationItem = (name: string) => {
  return {
    name: 'workLocation',
    label: name,
    component: <SearchInput placeholder={`请输入${name}`} />,
  };
};

export const getLocationColumn = (name: string) => {
  return { title: name, dataIndex: 'workLocation', width: 220 };
};

export const applyTimeItem = {
  name: 'applyTime',
  component: (
    <FixedWidthRangePicker
      placeholder={['作业实施时间起', '作业实施时间止']}
      style={{ width: '100%' }}
    />
  ),
};

export const levelItem = {
  name: 'workLevel',
  label: '作业级别',
  component: <SearchInput placeholder="请输入作业级别" />,
};

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '企业名称', dataIndex: 'enterpriseName', width: 200 },
  {
    title: '作业实施时间',
    dataIndex: 'applyTime',
    width: 200,
    render: (_v: any, record: any) => {
      const { actualStartTime, actualEndTime } = record;
      return `${actualStartTime} ~ ${actualEndTime}`;
    },
  },
  { title: '状态', dataIndex: 'workStatus', width: 120 },
  { title: '作业负责人', dataIndex: 'workLeader', width: 150 },
];

export const getLocationFormItem = (name: string) => {
  return { quickItemParams: [name, 'workLocation', 'Input', true] };
};

const { nameLengthRules, textAreaLengthRules, phoneLengthRules, phoneRules } = RULES;

export const BaseItems: Record<string, DynamicFormItem> = {
  number: { quickItemParams: ['编号', 'number', 'Input', false, '自动生成'], disabled: true },
  enterpriseName: {
    quickItemParams: ['作业申请单位', 'enterpriseName', 'ModalSelect', true, '请选择作业申请单位'],
    modalSelectInfo: companyModalSelectParams,
  },
  enterpriseCode: {
    type: 'hideItem',
    name: 'enterpriseCode',
    relatedValue: 'enterpriseName.enterpriseCode',
  },
};

type FrequencyItemKey =
  | 'applyTime'
  | 'workContent'
  | 'workLeaderContact'
  | 'workUnit'
  | 'workStatus'
  | 'workLeader'
  | 'workPlace'
  | 'actualTime'
  | 'isRiskWork'
  | 'supervisor'
  | 'workImplementPlace';

export const FrequencyItems: Record<FrequencyItemKey, DynamicFormItem> = {
  applyTime: {
    quickItemParams: ['作业申请时间', 'applyTime', 'DatePicker', true, '默认当前时间'],
    datePickerInfo: {
      showTime: defaultDateFormat,
      initValue: dayjs(),
    },
  },
  workContent: {
    quickItemParams: ['作业内容', 'workContent', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  workLeaderContact: {
    quickItemParams: ['作业负责人联系方式', 'workLeaderContact', 'Input', true],
    rules: [phoneRules, phoneLengthRules],
  },
  workUnit: { quickItemParams: ['作业单位', 'workUnit', 'Input', true], rules: [nameLengthRules] },
  workStatus: {
    quickItemParams: ['作业状态', 'workStatus', 'Select', true],
    optionsServices: () => dictConfigWrapper('work_status'),
    selectInfo: {
      onOptionsLoaded: defaultOptionValueWrapper('未签发'),
    },
  },
  workLeader: { quickItemParams: ['作业负责人', 'workLeader', 'Input', true] },
  workPlace: { quickItemParams: ['作业场所', 'workPlace', 'Input', false] },
  workImplementPlace: { quickItemParams: ['作业实施地点', 'workImplementPlace', 'Input', true] },
  supervisor: { quickItemParams: ['监护人', 'supervisor', 'Input', true] },
  actualTime: {
    quickItemParams: ['作业实施时间', 'actualTime', 'RangePicker', true],
    rangePickerInfo: {
      showTime: defaultDateFormat,
    },
  },
  isRiskWork: {
    quickItemParams: ['是否承包商作业', 'isRiskWork', 'Radio', true],
    options: defaultRadioOptions,
    radioInfo: {
      onOptionsLoaded: defaultOptionValueWrapper('否'),
    },
  },
};

export const SignItems: Record<string, DynamicFormItem> = {
  signedTime: {
    quickItemParams: ['签发时间', 'signedTime', 'DatePicker', false],
    datePickerInfo: { showTime: defaultDateFormat },
  },
  isWorkChanged: {
    quickItemParams: ['作业是否变更过', 'isWorkChanged', 'Radio', true],
    options: defaultRadioOptions,
    radioInfo: {
      onOptionsLoaded: defaultOptionValueWrapper('否'),
    },
  },
  changeReason: { quickItemParams: ['作业变更原因', 'changeReason', 'Input', false] },
  isCancel: {
    quickItemParams: ['作业是否取消', 'isCancel', 'Radio', true],
    options: defaultRadioOptions,
    radioInfo: {
      onOptionsLoaded: defaultOptionValueWrapper('否'),
    },
  },
  cancelReason: { quickItemParams: ['作业取消原因', 'cancelReason', 'Input', false] },
  associationOperator: {
    quickItemParams: ['关联其他特殊作业及安全作业票编号', 'associationOperator', 'Input', false],
    labelCol: { span: 12 },
    rules: [codeRules, codeLengthRules],
  },
  riskIdentification: { quickItemParams: ['风险辨识结果', 'riskIdentification', 'Input', true] },
};

type OpinionKey =
  | 'auditDepartmentOpinion'
  | 'jobOpinion'
  | 'unitOpinion'
  | 'completedOpinion'
  | 'departmentOpinion';

export const OpinionItem: Record<OpinionKey, DynamicFormItem[]> = {
  jobOpinion: [
    {
      quickItemParams: ['作业负责人意见', 'jobOpinion', 'Input', false],
      ...colParams,
      rules: [textAreaLengthRules],
    },
    {
      quickItemParams: ['', 'jobsignerName', 'Sign', false],
      signInfo: {
        signCallback: setSignTimeWrapper('jobsigtureTime'),
      },
    },
    { type: 'hideItem', name: 'jobsigtureTime' },
  ],
  unitOpinion: [...getOpinionItems('所在单位意见', 'unit')],
  completedOpinion: [...getOpinionItems('完工验收意见', 'completed')],
  departmentOpinion: [...getOpinionItems('安全管理部门意见', 'department')],
  auditDepartmentOpinion: [...getOpinionItems('审核部门意见', 'auditDepartment')],
};

export const savePersonItems: DynamicFormItem[] = [
  { quickItemParams: ['安全交底人', 'safetyLeader', 'Input', true] },
  { quickItemParams: ['接受交底人', 'acceptLeader', 'Input', true] },
];

export const BusinessItems: Record<string, DynamicFormItem> = {
  jobOpinion: { quickItemParams: ['作业负责人意见', 'jobOpinion', 'Input', false] },
  unitOpinion: { quickItemParams: ['所在单位意见', 'unitOpinion', 'Input', false] },
  departmentOpinion: { quickItemParams: ['安全管理部门意见', 'departmentOpinion', 'Input', false] },
  openFireOpinion: { quickItemParams: ['动火审批人意见', 'openFireOpinion', 'Input', false] },
  ticketOpinion: {
    quickItemParams: ['动火前，岗位当班班长验票情况', 'ticketOpinion', 'Input', false],
  },
  completedOpinion: { quickItemParams: ['完工验收意见', 'completedOpinion', 'Input', false] },
};

export const baseDateNames = ['applyTime', 'signedTime'];
