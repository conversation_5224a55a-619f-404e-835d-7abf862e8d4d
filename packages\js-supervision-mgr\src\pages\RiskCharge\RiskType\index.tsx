import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import { deleteRiskType, getRiskTypeDeatil, saveRiskType } from '@/services/riskCharge/riskType';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { getIndustriesList } from '@/services/enterpriseInfo';
import { history } from '@umijs/max';

let isChildAdd = false;

const RiskType: React.FC = () => {
  const [record, setRecord] = useState<any>({});
  const [columns, setColumns] = useState([...tableColumns]);
  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    setFormValues,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    selectedRowsDel,
    customEditPageHandle,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(null, setColumns, {
    subCode: () => fetchSelectOptions(getIndustriesList, 'subName', 'subCode'),
  });

  customButtons[0].onClick = () => {
    setRecord({});
    openFormWithAdd();
  };

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteRiskType);
  };

  const onSave = async (formData: any) => {
    const value = await saveHandler(formData, saveRiskType, data => {
      const newData = { ...data };
      if (isChildAdd) {
        Reflect.deleteProperty(newData, 'id');
      }
      return newData;
    });
    return value;
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getRiskTypeDeatil);
    setRecord(record);
    isChildAdd = false;
  };

  const openChild = (record: any) => {
    isChildAdd = true;
    const { typeName, id, subCode } = record;
    const parentRecord = { parentName: typeName, parentId: id, parentSubCode: subCode };
    customEditPageHandle(parentRecord);
    setRecord({ ...record, ...parentRecord });
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton onClick={() => openChild(record)}>新增子类型</CommonLinkButton>
            <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            <CommonLinkButton onClick={() => history.push(`/riskList?typeCode=${record.typeCode}`)}>
              风险清单
            </CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={onSave}
      formComp={<BaseInfoForm record={record} openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/risk/type/riskTypeList`}
        getFormValues={setFormValues}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default RiskType;
