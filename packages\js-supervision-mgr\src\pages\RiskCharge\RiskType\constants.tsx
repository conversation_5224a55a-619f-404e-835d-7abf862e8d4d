import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '风险类型';

export const filterItems = [
  {
    name: 'typeName',
    label: '风险类型名称',
    component: <SearchInput placeholder="请输入风险类型名称" />,
  },
  {
    name: 'subName',
    label: '所属行业',
    component: <SearchInput placeholder="请输入所属行业" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '类型名称', dataIndex: 'typeName' },
  { title: '类型编号', dataIndex: 'typeCode' },
  { title: '所属行业', dataIndex: 'subCode' },
  { title: '描述', dataIndex: 'description' },
];
