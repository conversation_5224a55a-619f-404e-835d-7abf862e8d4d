import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '风险分级管控清单';

export const exportUtl = '';

export const filterItems = [
  {
    name: 'controlMeasures',
    label: '管控措施',
    component: <SearchInput placeholder="请输入管控措施" />,
  },
  {
    name: 'riskLevel',
    label: '负责人',
    component: <SearchInput placeholder="请输入负责人" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '删除', ...baseButtonStyle },
  { text: '下载', ...baseButtonStyle },
  { text: '打印', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName' },
  { title: '风险点名称', dataIndex: 'riskName' },
  { title: '管控措施', dataIndex: 'controlMeasures' },
  { title: '负责人', dataIndex: 'responsiblePerson' },
  { title: '原因', dataIndex: 'riskAnalysis' },
  { title: '后果', dataIndex: 'hazardConsequence' },
  { title: '风险等级', dataIndex: 'riskGrade' },
  { title: '风险措施详情', dataIndex: 'implementationStatus' },
];
