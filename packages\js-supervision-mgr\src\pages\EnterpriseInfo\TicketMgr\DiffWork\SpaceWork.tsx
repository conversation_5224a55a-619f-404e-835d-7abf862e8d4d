import React from 'react';
import TicketMgr from '..';
import { DynamicFormItem, RULES } from 'jishan-components';
import {
  tableColumns,
  customButtons,
  getLocationItem,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  OpinionItem,
} from '../constants';
import SafetyMeasuresList from '../components/Measures';
import GasAnalysisTable from '../components/GasAnalysisTable';

const { nameLengthRules } = RULES;

const HomeTitle = '受限空间作业';

const newColumns = [...tableColumns];
newColumns.splice(3, 0, { title: '受限空间名称', dataIndex: 'workLocation', width: 220 });

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.applyTime,
  { quickItemParams: ['受限空间名称', 'workLocation', 'Input', true], rules: [nameLengthRules] },
  {
    quickItemParams: ['受限空间内原有介质名称', 'mediumName', 'Input', true],
    rules: [nameLengthRules],
  },
  FrequencyItems.workContent,
  FrequencyItems.workUnit,
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  FrequencyItems.supervisor,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  { type: 'blank' },
  FrequencyItems.actualTime,
  FrequencyItems.workPlace,
  FrequencyItems.workImplementPlace,
  { name: 'gasList', component: <GasAnalysisTable />, span: 24 },
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  { type: 'blank', span: 16 },
  ...OpinionItem.jobOpinion,
  ...OpinionItem.unitOpinion,
  ...OpinionItem.departmentOpinion,
  ...OpinionItem.completedOpinion,
  { type: 'hideItem', name: 'blindDrawingName' },
  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'toxicGasPjone' },
  { type: 'hideItem', name: 'toxicGasPjtwo' },
  { type: 'hideItem', name: 'flammGasPjone' },
  { type: 'hideItem', name: 'flammGasPjtwo' },
  { type: 'hideItem', name: 'toxicGasSdone' },
  { type: 'hideItem', name: 'toxicGasSdtwo' },
  { type: 'hideItem', name: 'flammGasSdone' },
  { type: 'hideItem', name: 'flammGasSdtwo' },
  { type: 'hideItem', name: 'gasParams' },
];

const SpaceWork: React.FC = () => {
  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[getLocationItem('受限空间名称'), applyTimeItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      keyName="受限空间"
      formItems={formItems}
    />
  );
};

export default SpaceWork;
