import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import FormInfo, { imgNames, cascadeFieldsMapCombined } from './components/FormInfo';
import {
  tableColumns,
  HomeTitle,
  exportUrl,
  exportTitle,
  filterItems,
  customButtons,
  importUrl,
} from './constants';
import {
  saveEnterpriseInfo,
  getEnterpriseInfoDetail,
  deleteEnterpriseInfo,
  getIndustriesList,
} from '@/services/enterpriseInfo';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const CompanyBaseInfoMgr: React.FC = () => {
  const [columns, setColumns] = useState([...tableColumns]);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    detailHandler,
    getUploadButton,
    selectedRowsDel,
    setFormValues,
    setExportEvent,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(null, setColumns, {
    industryCategory: () => fetchSelectOptions(getIndustriesList, 'subName', 'subCode'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteEnterpriseInfo);
  };

  customButtons[2].onClick = setExportEvent(exportUrl, exportTitle);

  customButtons[3].component = getUploadButton(importUrl);

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getEnterpriseInfoDetail);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getEnterpriseInfoDetail);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveEnterpriseInfo)}
      formComp={<FormInfo openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        imageFieldFormatControl: false,
        imageFieldNames: imgNames,
        batchImgName: 'attBatchParams',
        cascadeFieldsMap: cascadeFieldsMapCombined,
      }}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/enterpriseInformation/infoList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        getFormValues={setFormValues}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default CompanyBaseInfoMgr;
