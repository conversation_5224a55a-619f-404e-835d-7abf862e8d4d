import { request } from '@/utils/net';

export async function deleteGrading(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/riskLevel/controlList/removeGrading`, params);
}

export async function getGradingList(params: Record<string, any>) {
  return request.get(`${SPPREFIX}/riskLevel/controlList/gradingList`, {
    pageNo: 1,
    pageSize: 10000,
    ...params,
  });
}
