import React from 'react';
import BaseInfoForm, { imgNames as BaseImgNames } from './BaseInfoForm';
import RegisterInfoForm, { cascadeFieldsMap } from './RegisterInfoForm';
import { FormOpenType } from 'jishan-components';

interface FormInfoProps {
  openType: FormOpenType;
}

export const imgNames = BaseImgNames;

export const cascadeFieldsMapCombined = cascadeFieldsMap;

const FormInfo: React.FC<FormInfoProps> = ({ openType }) => {
  return (
    <>
      <BaseInfoForm openType={openType} />
      <RegisterInfoForm />
    </>
  );
};

export default FormInfo;
