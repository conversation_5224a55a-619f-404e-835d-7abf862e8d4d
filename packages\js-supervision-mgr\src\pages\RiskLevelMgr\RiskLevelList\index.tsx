import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import {
  tableColumns,
  HomeTitle,
  filterItems,
  customButtons,
  optionsWrapper,
  exportUrl,
} from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import {
  deleteAssessment,
  getAssessmentDetail,
  saveAssessment,
} from '@/services/riskLevelMgr/riskLevelList';

const RiskLevelList: React.FC = () => {
  const [columns, setColumns] = useState([...tableColumns]);
  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    setFormValues,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    edit<PERSON>and<PERSON>,
    selectedRowsDel,
    detailHandler,
    setExportEvent,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(null, setColumns, {
    accidentProbability: optionsWrapper('accidentProbability'),
    accidentFrequency: optionsWrapper('accidentFrequency'),
    accidentConsequence: optionsWrapper('accidentConsequence'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteAssessment);
  };

  customButtons[2].onClick = setExportEvent(exportUrl, HomeTitle);

  const openEdit = (record: any) => {
    editHandler(record, getAssessmentDetail);
  };

  const openDetail = (record: any) => {
    detailHandler(record, getAssessmentDetail);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveAssessment)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
      formFieldOptions={{
        imageFieldFormatControl: false,
        imageFieldNames: ['afterPhotos'],
        batchImgName: 'riskBatchParams',
      }}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/riskLevel/scaleList/assessmentList`}
        getFormValues={setFormValues}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default RiskLevelList;
