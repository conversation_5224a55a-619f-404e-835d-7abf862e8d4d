import React from 'react';
import UploadCustom from '../../../BaseFormComponents/UploadCustom';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { DynamicFormItem } from '../interface';
import { FormOpenType } from '../../FormContainer';
import { getCompName } from '../../../utils/commonFunction';
import { getFileList, FileUploadValue } from '../../../utils/form';
import './components.less';

interface DFImgUploadProps {
  value?: FileUploadValue; // 图片路径
  onChange?: (value: string) => void;
  openType: FormOpenType;
  item: DynamicFormItem;
}

const DFImgUpload: React.FC<DFImgUploadProps> = ({ value, onChange, openType, item }) => {
  const {
    imagesImg,
    buttonTitle = '上传附件',
    maxNum = 1,
    emptyText = '未上传图片',
  } = item?.imgInfo || {};

  const valueList = getFileList(value);
  // 没有图片时显示空态
  if (!valueList.length && openType === 'view') {
    return (
      <div className={getCompName('empty')}>
        <ExclamationCircleOutlined className={getCompName('empty-icon')} />
        <span>{emptyText}</span>
      </div>
    );
  }

  return (
    <UploadCustom
      value={valueList}
      onChange={onChange}
      imagesImg={imagesImg}
      buttonTitle={buttonTitle}
      // 非查看模式下限制 maxNum，避免 Upload 在非编辑模式下图片数量小于 maxNum 时显示上传的空图片槽
      maxNum={openType !== 'view' ? maxNum : valueList.length}
    />
  );
};

export default DFImgUpload;
