import React, { useState, useRef } from 'react';
import '../index.less';
import { Button } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

interface Alert {
  title: string;
  count: number;
}
interface AlertInfoCardProps {
  title: string;
  alerts: Alert[];
}

const PAGE_SIZE = 4; // 每页显示4条
const INTERVAL = 5000; // 轮播间隔，单位ms

const AlertInfoCard: React.FC<AlertInfoCardProps> = ({ title, alerts }) => {
  const totalCount = alerts.reduce((sum, alert) => sum + alert.count, 0);
  const pageCount = Math.ceil(alerts.length / PAGE_SIZE);
  const [page, setPage] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 自动轮播
  // useEffect(() => {
  //   if (pageCount <= 1) return;
  //   timerRef.current = setInterval(() => {
  //     setPage(prev => (prev + 1) % pageCount);
  //   }, INTERVAL);
  //   return () => {
  //     if (timerRef.current) clearInterval(timerRef.current);
  //   };
  // }, [pageCount]);

  // 主动切换时重置自动轮播
  const goToPage = (newPage: number) => {
    setPage(newPage);
    if (timerRef.current) clearInterval(timerRef.current);
    if (pageCount > 1) {
      timerRef.current = setInterval(() => {
        setPage(prev => (prev + 1) % pageCount);
      }, INTERVAL);
    }
  };

  const handlePrev = () => {
    goToPage((page - 1 + pageCount) % pageCount);
  };

  const handleNext = () => {
    goToPage((page + 1) % pageCount);
  };

  return (
    <div className="alert-info-card">
      {/* 左侧部分 */}
      <div className="alert-info-card__left">
        <div className="alert-info-card__title">{title}</div>
        <div className="alert-info-card__total">{totalCount}条</div>
      </div>
      {/* 右侧部分 */}
      <div className="alert-info-card__right">
        <div className="alert-info-card__carousel-wrapper">
          {/* 主动切换按钮 */}
          {pageCount > 1 && (
            <>
              <Button icon={<LeftOutlined />} onClick={handlePrev} className="page-btn" />
              <Button onClick={handleNext} icon={<RightOutlined />} className="page-btn" />
            </>
          )}
          <div
            className="alert-info-card__carousel"
            style={{
              transform: `translateX(-${page * 100}%)`,
            }}
          >
            {Array.from({ length: pageCount }).map((_, i) => (
              <div className="alert-info-card__page" key={i} style={{ minWidth: '100%' }}>
                {alerts.slice(i * PAGE_SIZE, (i + 1) * PAGE_SIZE).map((alert, idx) => (
                  <div key={idx} className="alert-info-card__alert">
                    <span className="alert-info-card__alert-title">
                      {alert.title}: {alert.count}条
                    </span>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default AlertInfoCard;
