import React from 'react';
import {
  DynamicForm,
  DynamicFormItem,
  SectionTitle,
  buildRules,
  RULES,
  JSCUtils,
  FormOpenType,
} from 'jishan-components';
import { getIndustriesList } from '@/services/enterpriseInfo';
import { fetchSelectOptions } from '@/utils/commonFunction';
import imagesImg from '@/assets/images/common/image.png';

const { nameLengthRules, codeRules, codeLengthRules } = RULES;
const { setOption } = JSCUtils;

const formItems: DynamicFormItem[] = [
  {
    label: '企业名称',
    name: 'enterpriseName',
    rules: buildRules('请输入企业名称', true, nameLengthRules),
  },
  {
    label: '统一社会信用代码',
    name: 'enterpriseCode',
    rules: buildRules('请输入统一社会信用代码', true, codeRules, codeLengthRules),
  },
  {
    label: '所属专委会',
    name: 'economicType',
    type: 'Select',
    rules: buildRules('请选择所属专委会', true),
    options: [
      setOption('危化'),
      setOption('非煤矿山'),
      setOption('冶金工贸'),
      setOption('综合交通'),
      setOption('文化旅游'),
      setOption('民爆物品'),
      setOption('特种设备'),
      setOption('住建'),
      setOption('燃气'),
      setOption('消防'),
      setOption('能源'),
      setOption('校园'),
      setOption('农业'),
      setOption('餐饮住宿'),
    ],
  },
  {
    label: '行业类别',
    name: 'industryCategory',
    type: 'Select',
    rules: buildRules('请选择行业类别', true),
    optionsServices: () => fetchSelectOptions(getIndustriesList, 'subName', 'subCode'),
  },
  {
    label: '职工人数',
    name: 'employeeCount',
    type: 'IntegerInput',
    rules: buildRules('请输入职工人数', false, { min: 0, message: '人数不能为负数' }),
  },
  {
    label: '企业规模',
    name: 'enterpriseScale',
    type: 'Select',
    rules: buildRules('请选择企业规模', false),
    options: [
      setOption('大型企业'),
      setOption('中型企业'),
      setOption('小型企业'),
      setOption('微型企业'),
    ],
  },
  {
    label: '企业面积',
    name: 'enterpriseArea',
    type: 'IntegerInput',
    rules: buildRules('请输入企业面积', false),
  },
  {
    label: '法定代表人',
    name: 'legalRepresentative',
    rules: buildRules('请输入法定代表人', false),
  },
  {
    label: '工商登记状态',
    name: 'businessRegistrationStatus',
    type: 'Select',
    rules: buildRules('请选择工商登记状态', false),
    options: [
      setOption('存续'),
      setOption('在业'),
      setOption('吊销'),
      setOption('注销'),
      setOption('迁入'),
      setOption('迁出'),
      setOption('停业'),
      setOption('清算'),
    ],
  },
  {
    label: '当前状态',
    name: 'currentStatus',
    type: 'Select',
    rules: buildRules('请选择当前状态', false),
    options: [setOption('生产'), setOption('停产'), setOption('在建'), setOption('新批')],
    // 只有冶金用户显示该字段，具体可通过业务逻辑控制显示
  },
  {
    label: '经营期限自',
    name: 'businessPeriodStart',
    type: 'DatePicker',
    rules: buildRules('请选择经营期限起始日期', true),
  },
  {
    label: '经营期限至',
    name: 'businessPeriodEnd',
    type: 'DatePicker',
    rules: buildRules('请选择经营期限截止日期', false),
  },
  {
    label: '上级主管部门',
    name: 'superiorRegulatoryDepartment',
    rules: buildRules('请输入上级主管部门', false),
  },
  {
    label: '经营范围',
    name: 'businessScope',
    type: 'TextArea',
    rules: buildRules('请输入经营范围', false),
  },
  {
    label: '企业边界',
    name: 'enterpriseBoundary',
    rules: buildRules('请通过地图绘制企业边界', false),
    // 实际应为地图控件，这里暂用Input占位
  },
  {
    label: '风险四色图',
    name: 'riskColorMap',
    type: 'ImgUpload',
    imgInfo: { imagesImg, maxNum: 8 },
  },
  {
    label: '平面图',
    name: 'floorPlan',
    type: 'ImgUpload',
    imgInfo: { imagesImg },
  },
  {
    label: '建筑图',
    name: 'buildingLayout',
    type: 'ImgUpload',
    imgInfo: { imagesImg },
  },
];

export const BaseInfoFormNames = formItems.map(item => item.name);

export const imgNames = ['floorPlan', 'buildingLayout', 'riskColorMap'];

interface BaseInfoFormProps {
  openType: FormOpenType;
}

// TODO 冶金用户的差分处理
const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType }) => {
  return (
    <>
      <SectionTitle title="基本信息" />
      <DynamicForm items={formItems} openType={openType} />
    </>
  );
};

export default BaseInfoForm;
