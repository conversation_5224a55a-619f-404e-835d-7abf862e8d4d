import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  OpinionItem,
} from '../constants';
import { FixedWidthSelect, JSCUtils, DynamicFormItem } from 'jishan-components';
import SafetyMeasuresList from '../components/Measures';
import BlindParams from '../components/BlindParams';
import imagesImg from '@/assets/images/common/image.png';

const HomeTitle = '盲板抽堵作业';

const { setOption } = JSCUtils;

const defaultCatOptions = [setOption('抽盲板'), setOption('堵盲板')];

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  { title: '作业类别', dataIndex: 'workCategory', width: 180 },
  { title: '作业单位', dataIndex: 'workUnit', width: 180 },
);

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.workUnit,
  {
    quickItemParams: ['作业类别', 'workCategory', 'Select', true],
    options: defaultCatOptions,
    selectInfo: { onOptionsLoaded: () => '堵盲板' },
  },
  { name: 'equipmentName', component: <BlindParams />, span: 24 },
  { quickItemParams: ['盲板编制图', 'blindDrawing', 'ImgUpload', true], imgInfo: { imagesImg } },
  { quickItemParams: ['编制人', 'blindDrawingName', 'Input', true] },
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  FrequencyItems.supervisor,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  FrequencyItems.actualTime,
  FrequencyItems.workPlace,
  FrequencyItems.workImplementPlace,
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  { type: 'blank', span: 16 },
  ...OpinionItem.jobOpinion,
  ...OpinionItem.unitOpinion,
  ...OpinionItem.completedOpinion,
  { type: 'hideItem', name: 'pipelineName' },
  { type: 'hideItem', name: 'pipelineMedium' },
  { type: 'hideItem', name: 'pipelineTemperature' },
  { type: 'hideItem', name: 'pipelineStress' },
  { type: 'hideItem', name: 'pipelineMaterial' },
  { type: 'hideItem', name: 'pipelineSpecification' },
  { type: 'hideItem', name: 'pipelineNumber' },
  { type: 'hideItem', name: 'actualWorkTime' },
  { type: 'hideItem', name: 'attBatchParams' },
];

const formFilterItems = [
  applyTimeItem,
  {
    name: 'workCategory',
    label: '作业类别',
    component: <FixedWidthSelect placeholder="请选择作业类别" options={defaultCatOptions} />,
  },
];

const BlindPlateWork: React.FC = () => {
  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={formFilterItems}
      customButtons={customButtons}
      tableColumns={newColumns}
      keyName={HomeTitle}
      formItems={formItems}
      imgNames={['blindDrawing']}
    />
  );
};

export default BlindPlateWork;
