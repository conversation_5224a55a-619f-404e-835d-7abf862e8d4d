import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm, { fileNames } from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import {
  deleteCertificate,
  getCertificateDetail,
  saveCertificate,
} from '@/services/certificateMgr';
import FilePreview from '@/components/FilePreview';
import { getFileNameFromUrl } from '@/utils/commonFunction';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

let fileUrl = '';

const now = new Date();

const CertificateMgr: React.FC = () => {
  const [previewOpen, setPreviewOpen] = useState(false);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    save<PERSON><PERSON><PERSON>,
    edit<PERSON><PERSON><PERSON>,
    detail<PERSON><PERSON><PERSON>,
    selectedRowsDel,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteCertificate);
  };

  const handleBusinessTermTime = (params: any) => {
    const { businessTermDate, ...rest } = params;
    let validityStart;
    let validityEnd;
    if (Array.isArray(businessTermDate) && businessTermDate.length === 2) {
      validityStart = businessTermDate[0]?.format('YYYY-MM-DD');
      validityEnd = businessTermDate[1]?.format('YYYY-MM-DD');
    }
    return {
      ...rest,
      validityStart,
      validityEnd,
    };
  };

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getCertificateDetail);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getCertificateDetail);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '附件',
      dataIndex: 'attachmentUrl',
      width: 180,
      render: (text: string, record: any) => {
        // 获取当前日期和有效期截止日期
        const validityEnd = record.validityEnd ? new Date(record.validityEnd) : null;
        // 判断是否过期
        const isExpired = validityEnd && now > validityEnd;
        return (
          <CommonLinkButton
            onClick={() => {
              fileUrl = text;
              setPreviewOpen(true);
            }}
            style={isExpired ? { color: 'red' } : {}}
          >
            {getFileNameFromUrl(text)}
          </CommonLinkButton>
        );
      },
    },
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveCertificate)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        normalFileNames: fileNames,
        fileStringFormat: true,
      }}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/certificateInfo/certificateList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        searchParamsHandle={handleBusinessTermTime}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
        scroll={{ x: 1400 }}
      />
      <FilePreview path={fileUrl} open={previewOpen} onClose={() => setPreviewOpen(false)} />
    </CommonPage>
  );
};

export default CertificateMgr;
