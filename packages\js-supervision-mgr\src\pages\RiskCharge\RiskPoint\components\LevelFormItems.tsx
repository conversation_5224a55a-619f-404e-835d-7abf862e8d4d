import React from 'react';
import { DynamicForm, DynamicFormItem, SectionTitle } from 'jishan-components';
import { Button, FormInstance, message, Table } from 'antd';
import styles from '../index.less';
import { levelColumns } from '../constants';
import { isProductInRange } from '@/utils/commonFunction';

const LevelFormItems: React.FC<{
  form?: FormInstance<any>;
  items: DynamicFormItem[];
  analysisItems: DynamicFormItem[];
  levelInfo: Record<string, any>[];
}> = ({ form, items, levelInfo = [], analysisItems }) => {
  // 点击按钮时校验并计算
  const analysis = () => {
    const values = form?.getFieldsValue?.() || {};
    const { accidentConsequence, accidentFrequency, accidentProbability } = values;
    if (!accidentConsequence) {
      message.warning('请选择事故后果');
      return;
    }
    if (!accidentFrequency) {
      message.warning('请选择频繁程度');
      return;
    }
    if (!accidentProbability) {
      message.warning('请选择事故发生可能性');
      return;
    }
    const product = accidentConsequence * accidentFrequency * accidentProbability;
    form?.setFieldValue('dValue', product);
    let riskLevel = '';
    for (const item of levelInfo) {
      if (isProductInRange(product, item.dValue)) {
        riskLevel = item.description;
        break;
      }
    }
    form?.setFieldValue('riskLevel', riskLevel);
  };

  return (
    <>
      <DynamicForm items={items} form={form} openType="add" />
      <div className={styles.buttonContainer}>
        <Button type="primary" onClick={analysis}>
          开始分析
        </Button>
      </div>
      <SectionTitle title="分析结果" />
      <DynamicForm items={analysisItems} form={form} openType="add" />
      <SectionTitle title="风险定级" />
      <Table
        // @ts-ignore
        columns={levelColumns}
        dataSource={levelInfo}
        rowKey="id"
        pagination={false}
        style={{ width: '100%' }}
      />
    </>
  );
};
export default LevelFormItems;
