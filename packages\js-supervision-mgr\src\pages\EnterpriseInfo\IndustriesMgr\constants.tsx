import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '变更行业';

export const filterItems = [
  {
    name: 'subName',
    label: '行业名称',
    component: <SearchInput placeholder="请输入行业名称" />,
  },
  {
    name: 'subCode',
    label: '行业编码',
    component: <SearchInput placeholder="请输入行业编码" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '行业名称', dataIndex: 'subName', width: 300 },
  { title: '行业编码', dataIndex: 'subCode', width: 200 },
  { title: '描述', dataIndex: 'description' },
];
