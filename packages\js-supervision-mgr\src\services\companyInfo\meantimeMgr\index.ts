import { request } from '@/utils/net';

export async function saveMeantime(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/threemeantime/saveMeantime`, params);
}

export async function getMeantimeDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/threemeantime/meantimeDetail`, {
    id,
  });
}

export async function deleteMeantime(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/threemeantime/removeMeantime`, params);
}
