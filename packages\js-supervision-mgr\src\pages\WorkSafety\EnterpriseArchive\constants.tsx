import React from 'react';
import { SearchInput } from 'jishan-components';

export const HomeTitle = '一企一档';

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入企业名称" />,
  },
  {
    name: 'certificateName',
    label: '法定代表人',
    component: <SearchInput placeholder="请输入法定代表人" />,
  },
  {
    name: 'hazardLevel',
    label: '工商登记状态',
    span: 4,
    component: <SearchInput placeholder="请选择工商登记状态" style={{ width: '100%' }} />,
  },
  {
    name: 'hazardType',
    label: '行业分类',
    span: 4,
    component: <SearchInput placeholder="请选择行业分类" style={{ width: '100%' }} />,
  },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true },
  { title: '法定代表人', dataIndex: 'legalRepresentative', width: '15%' },
  { title: '工商登记状态', dataIndex: 'businessRegistrationStatus', width: '15%' },
  { title: '行业分类', dataIndex: 'industryCategory', width: '15%' },
  { title: '企业经济类型', dataIndex: 'enterpriseEconomy', width: '15%' },
];
