import { Input } from 'antd';
import React from 'react';

interface DFIntegerInputProps {
  placeholder: string;
  value?: string | number;
  onChange?: (e: any) => void;
}

const DFIntegerInput: React.FC<DFIntegerInputProps> = ({ value, onChange, placeholder }) => {
  // 只允许输入整数
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value.replace(/[^\d-]/g, ''); // 只保留数字和负号
    if (onChange) {
      onChange(val);
    }
  };
  return <Input value={value} onChange={handleChange} placeholder={placeholder} />;
};

export default DFIntegerInput;
