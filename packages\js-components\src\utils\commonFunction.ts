import { PREFIX_SERVER } from '../global';

export const getOptionsFromDict = (dicts: any[]) => {
  if (dicts.length > 0) {
    return dicts.map(item => ({
      label: item.dictValue,
      value: item.dictCode,
    }));
  }
  return [];
};

export const getCompName = (name: string) => {
  return `jsc-${name}`;
};

export const setOption = (label: string) => {
  return { label, value: label };
};

/**
 * 处理表单值中的数组对象，将嵌套属性提升到顶层
 * @param formValues 表单值对象
 * @param name 要处理的属性名
 * @returns 处理后的表单值对象
 */
export function flattenArrayObjects<T extends Record<string, any>>(
  formValues: T,
  name: string,
): Record<string, any> {
  if (!(name in formValues) || !Array.isArray(formValues[name])) {
    return formValues;
  }
  const arrayValue = formValues[name];
  const result: Record<string, any> = { ...formValues };
  arrayValue.forEach((item: any, index: number) => {
    if (typeof item === 'object' && item !== null && !Array.isArray(item)) {
      Object.entries(item).forEach(([key, value]) => {
        const newKey = `${key}_${index}`;
        result[newKey] = value;
      });
    }
  });
  delete result[name];
  return result;
}

/**
 * 将对象中所有带下划线+数字的属性封装成一个对象数组，并赋值给传入的 name。
 * 例如：{ a_0: 1, b_0: 2, a_1: 3, b_1: 4 } => { name: [{a:1, b:2}, {a:3, b:4}] }
 */
export function unflattenArrayObjects<T extends Record<string, any>>(
  flatValues: T,
  name: string,
): Record<string, any> {
  const result: Record<string, any> = { ...flatValues };
  const arrayMap: Record<number, Record<string, any>> = {};
  // 匹配形如 xxx_数字 的 key
  const regex = /^(.+)_([0-9]+)$/;
  Object.keys(flatValues).forEach(key => {
    const match = key.match(regex);
    if (match) {
      const prop = match[1];
      const index = parseInt(match[2], 10);
      if (!arrayMap[index]) {
        arrayMap[index] = {};
      }
      arrayMap[index][prop] = flatValues[key];
      // 删除原有的扁平 key
      delete result[key];
    }
  });
  // 按索引顺序组装数组
  const arr = Object.keys(arrayMap)
    .sort((a, b) => Number(a) - Number(b))
    .map(k => arrayMap[Number(k)]);
  result[name] = arr;
  return result;
}

/**
 * 拆分 formValues 中 name 及 name_number 的时间区间数组，分别赋值给 startName、endName 对应的新 key
 * @param {Object} formValues - 表单对象
 * @param {string} name - 需要查找的 key（如 'date'）
 * @param {string} startName - 拆分后开始时间的 key（如 'startDate'）
 * @param {string} endName - 拆分后结束时间的 key（如 'endDate'）
 * @returns {Object} 新的表单对象
 */
function splitRangeToStartEnd(formValues: any, name: string, startName: string, endName: string) {
  const result = { ...formValues };
  const regex = new RegExp(`^${name}(?:_(\\d+))?$`);
  Object.keys(formValues).forEach(key => {
    const match = key.match(regex);
    if (match && Array.isArray(formValues[key]) && formValues[key].length === 2) {
      const suffix = match[1] !== undefined ? `_${match[1]}` : '';
      result[`${startName}${suffix}`] = formValues[key][0];
      result[`${endName}${suffix}`] = formValues[key][1];
      delete result[key];
    }
  });
  return result;
}

/**
 * 将 formValues 中 startName、endName 及其带编号的 key 合并为 name 或 name_number 的时间区间数组
 * @param {Object} formValues - 表单对象
 * @param {string} name - 合并后的 key（如 'date'）
 * @param {string} startName - 开始时间的 key（如 'startDate'）
 * @param {string} endName - 结束时间的 key（如 'endDate'）
 * @returns {Object} 新的表单对象
 */
function mergeStartEndToRange(formValues: any, name: string, startName: string, endName: string) {
  const result = { ...formValues };
  const regex = new RegExp(`^${startName}(?:_(\\d+))?$`);
  Object.keys(formValues).forEach(key => {
    const match = key.match(regex);
    if (match) {
      const suffix = match[1] !== undefined ? `_${match[1]}` : '';
      const startValue = formValues[key];
      const endKey = `${endName}${suffix}`;
      if (endKey in formValues) {
        const endValue = formValues[endKey];
        const rangeKey = `${name}${suffix}`;
        result[rangeKey] = [startValue, endValue];
        delete result[key];
        delete result[endKey];
      }
    }
  });
  return result;
}

// 封装通用的模板下载链接生成函数
export function getTemplateDownloadUrl(fileName: string, objectName: string): string {
  const BASE_URL = `${PREFIX_SERVER}/api/downloadFile`;
  const QUERY_PARAMS = new URLSearchParams({
    bucket: 'basicinformation',
    fileName,
    objectName,
  });
  return `${BASE_URL}?${QUERY_PARAMS.toString()}`;
}

export const JSCUtils = {
  setOption,
  getCompName,
  flattenArrayObjects,
  unflattenArrayObjects,
  splitRangeToStartEnd,
  mergeStartEndToRange,
  getTemplateDownloadUrl,
};

/**
 * 处理下载文件
 * @param filePath 路径
 * @returns
 */
export function downloadFile(filePath: any) {
  let url = filePath;
  if (!filePath) {
    return '';
  }
  if (!filePath.startsWith('http')) {
    url = new URL(`${location.origin}${filePath}`);
  }
  const urlParams = new URLSearchParams(url.searchParams);
  // const fileName = urlParams.get('fileName');
  const objectName = urlParams.get('objectName');
  try {
    return `http://10.10.193.159:9091/storage${filePath}&fullfilename=${objectName}`;
  } catch (error) {
    console.error('Invalid  URL  format', error);
    return '';
  }
}

export function getValueByPath(obj: any, path: string): any {
  if (!obj || !path) return undefined;
  const keys = path.split('.');
  let result = obj;
  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key];
    } else {
      return undefined;
    }
  }
  return result;
}

/**
 * 解析 relatedValue 字符串，获取目标值
 * @param store 源对象
 * @param relatedValue 形如 "A.B.C"
 */
export function getRelatedValue(
  formStore: Record<string, any>,
  getName: (baseName: string) => string,
  relatedValue?: string,
): any {
  if (!relatedValue) return undefined;
  const paths = relatedValue.split('.');
  const rootKey = getName(paths[0]);
  let value = formStore[rootKey];
  // 从 paths[1] 开始依次取值
  for (let i = 1; i < paths.length; i += 1) {
    if (value === null || value === undefined) break;
    value = value[paths[i]];
  }
  return value;
}
