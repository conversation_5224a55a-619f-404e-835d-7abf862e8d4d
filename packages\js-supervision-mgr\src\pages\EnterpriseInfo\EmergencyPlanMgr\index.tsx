import React from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons, importUrl } from './constants';
import { deletePlan, getPlanDetail, savePlan } from '@/services/companyInfo/emergencyPlanMgr';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const EmergencyPlanMgr: React.FC = () => {
  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    deleteHandler,
    selectedRowsDel,
    getUploadButton,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deletePlan);
  };

  customButtons[2].component = getUploadButton(importUrl);

  const handleFilingDate = (params: any) => {
    const { filingDate, ...rest } = params;
    let startTime;
    let endTime;
    if (Array.isArray(filingDate) && filingDate.length === 2) {
      startTime = filingDate[0]?.format('YYYY-MM-DD');
      endTime = filingDate[1]?.format('YYYY-MM-DD');
    }
    return {
      ...rest,
      startTime,
      endTime,
    };
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getPlanDetail);
  };

  const handleDeleteOne = (record: any) => {
    deleteHandler([record.id], deletePlan);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => handleDeleteOne(record)}>删除</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, savePlan)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/contingencyPlan/planList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        searchParamsHandle={handleFilingDate}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
        scroll={{ x: 1400 }}
      />
    </CommonPage>
  );
};

export default EmergencyPlanMgr;
