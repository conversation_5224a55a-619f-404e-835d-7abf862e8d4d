import React from 'react';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';

import CommonPersonPage from '../CommonPersonPage';
import BaseInfoForm from './components/BaseInfoForm';

const WorkerMgr: React.FC = () => {
  return (
    <CommonPersonPage
      BaseInfoForm={BaseInfoForm}
      homeTitle={HomeTitle}
      customButtons={customButtons}
      filterItems={filterItems}
      tableColumns={tableColumns}
    />
  );
};

export default WorkerMgr;
