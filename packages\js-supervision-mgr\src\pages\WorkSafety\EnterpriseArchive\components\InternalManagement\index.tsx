import React, { useState } from 'react';
import { ListRequest } from 'jishan-components';
import { tableColumns, filterItems, mockData } from './constants';
import './index.less';

interface InternalManagementProps {
  data?: any;
}

const InternalManagement: React.FC<InternalManagementProps> = () => {
  const [updateTrigger] = useState(0);

  // 模拟 API 请求处理
  const serviceResHandle = (_res: any) => {
    // 在实际项目中，这里会处理真实的 API 响应
    // 现在返回模拟数据
    return {
      data: mockData.data,
      totalRecords: mockData.total,
    };
  };

  return (
    <div className="internal-management">
      <ListRequest
        apiUrl="/api/internal-management/list"
        columns={tableColumns}
        filterItems={filterItems}
        updateTrigger={updateTrigger}
        rowSelection={false}
        customButtons={[]}
        showSizeChanger={true}
        scrollYDelta={0}
        serviceResHandle={serviceResHandle}
        method="GET"
        tableBordered={true}
        scroll={{ x: 1400 }}
      />
    </div>
  );
};

export default InternalManagement;
