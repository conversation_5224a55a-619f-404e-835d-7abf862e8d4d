import React, { useState } from 'react';
import { ListRequest } from 'jishan-components';
import { tableColumns, filterItems, mockData } from './constants';
import './index.less';

interface InternalManagementProps {
  data?: any;
}

const InternalManagement: React.FC<InternalManagementProps> = () => {
  const [updateTrigger] = useState(0);

  // 模拟 API 请求处理
  const serviceResHandle = (_res: any) => {
    // 在实际项目中，这里会处理真实的 API 响应
    // 现在返回模拟数据
    return {
      data: mockData.data,
      totalRecords: mockData.total,
    };
  };

  return (
    <div className="internal-management">
      <ListRequest
        apiUrl={`${SPPREFIX}/safetyProduct/oneCompanyOneFile/list`}
        columns={tableColumns}
        filterItems={filterItems}
        updateTrigger={updateTrigger}
        showSizeChanger={true}
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
        serviceResHandle={serviceResHandle}
        inlineButtons
        method="GET"
        tableBordered={true}
      />
    </div>
  );
};

export default InternalManagement;
