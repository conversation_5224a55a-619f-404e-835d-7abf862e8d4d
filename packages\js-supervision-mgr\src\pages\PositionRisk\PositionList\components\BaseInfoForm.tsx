import React from 'react';
import { DynamicForm, DynamicFormItem, FormOpenType, RULES } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';
import { dictConfigWrapper } from '@/utils/commonFunction';

const { textAreaLengthRules, phoneRules, phoneLengthRules, nameLengthRules } = RULES;

export const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
  },
  { quickItemParams: ['岗位名称', 'positionName', 'Input', true], rules: [nameLengthRules] },
  {
    quickItemParams: ['岗位类别', 'positionCategory', 'Select', true],
    optionsServices: () => dictConfigWrapper('position_category'),
  },
  {
    quickItemParams: ['岗位等级', 'positionLevel', 'Select', true],
    optionsServices: () => dictConfigWrapper('position_level'),
  },
  {
    quickItemParams: ['岗位风险等级', 'riskLevel', 'Select', true],
    optionsServices: () => dictConfigWrapper('control_level'),
  },
  { quickItemParams: ['岗位状态', 'positionStatus', 'Input', true] },
  {
    quickItemParams: ['岗位职责', 'responsibilities', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  { quickItemParams: ['所属部门', 'department', 'Input', true] },
  { quickItemParams: ['工作地点', 'workLocation', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['学历要求', 'educationRequirement', 'Input', false] },
  { quickItemParams: ['专业要求', 'majorRequirement', 'Input', false] },
  { quickItemParams: ['责任人', 'responsiblePerson', 'Input', true] },
  {
    quickItemParams: ['责任人联系方式', 'contactPhone', 'Input', true],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['工作经验要求', 'experienceRequirement', 'Input', false] },
  { quickItemParams: ['技能要求', 'skillRequirements', 'Input', false] },
  { quickItemParams: ['备注', 'remarks', 'TextArea', false], rules: [textAreaLengthRules] },
  { type: 'hideItem', name: 'enterpriseCode', relatedValue: 'enterpriseName.enterpriseCode' },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return <DynamicForm items={formItems} openType={openType} form={form} />;
};

export default BaseInfoForm;
