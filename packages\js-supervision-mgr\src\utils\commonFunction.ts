/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
// import moment from 'moment';
import { sysDictList } from '@/services/common/common';
import { message } from 'antd';

export const getOptionsFromDict = (dicts: any[]) => {
  if (dicts.length > 0) {
    return dicts.map(item => ({
      label: item.dictValue,
      value: item.dictCode,
    }));
  }
  return [];
};

// 生成 code 到 label 的映射
export const createCodeLabelMap = (options: { value: string; label: string }[]) => {
  return options.reduce((acc, option) => {
    acc[option.value] = option.label;
    return acc;
  }, {} as Record<string, string>);
};

export const fetchOptions = async (
  fetchFunction: () => Promise<any>,
  setOptions: (options: any[]) => void,
  labelKey: string,
  valueKey: string,
  callback?: (data: any[]) => void,
) => {
  try {
    const res = await fetchFunction();
    if (res.code === '200') {
      const {
        data: { data },
      } = res;
      if (Array.isArray(data)) {
        const options = data.map(item => ({
          label: item[labelKey],
          value: item[valueKey],
        }));
        setOptions(options);
        if (callback) callback(data);
      }
    } else {
      message.error(res.msg);
    }
  } catch (error) {
    message.error('获取数据失败');
  }
};

export const setFormDataFromFileUrl = (fileUrl: string) => {
  if (!fileUrl) {
    return { fileUrl: [] };
  }
  // 提取 fileName
  const urlParams = new URLSearchParams(fileUrl.split('?')[1]);
  const fileName = urlParams.get('fileName') || '';
  return {
    name: fileName,
    downloadUrl: fileUrl,
  };
};

/**
 * 简单求和
 * @param list
 * @returns
 */
export function getTotal(list: any, field?: string) {
  return list?.reduce((cur: any, next: any) => {
    let value = Number(cur);
    value += field ? Number(next[field] || 0) : Number(next.value || 0);
    return value;
  }, 0);
}

// 获取嵌套值（支持 item.subItem.field 格式）
export function getNestedValue(obj: any, path: string) {
  return path.split('.').reduce((o: { [x: string]: any }, p: string | number) => o?.[p], obj);
}

export function mapData(source: any, path: string) {
  // 处理空路径或空数据源
  if (!path || !source) return undefined;

  const segments = path
    .split(/(?:\]\.|\.?$$].?)/g)
    .flatMap((s: string) => s.split('.'))
    .filter(Boolean);

  let current = [source]; // 始终保持数组形式处理

  for (const segment of segments) {
    // 处理数组展开语法 []
    if (segment === '') {
      current = current.flatMap(item => (Array.isArray(item) ? item : [item]));
    } else if (segment.includes('[')) {
      // 处理带索引的数组 sales[0]
      const [key, index] = segment.split(/\[|$$/g).filter(Boolean);
      current = current.flatMap(obj => {
        const arr = obj?.[key];
        return index ? [arr?.[Number(index)]] : arr || [];
      });
    } else {
      // 处理普通对象属性
      current = current.flatMap(obj => {
        const value = obj?.[segment];
        return value !== undefined ? value : [];
      });
    }
  }

  return current;
}

export async function exportFile(formValues: any, url: string, title?: string) {
  const queryString = new URLSearchParams(formValues).toString();
  const fullUrl = `${url}?${queryString}`;
  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: { access_token: localStorage.getItem('jishan_token') || '' },
    });
    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${title || 'export_data'}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } else {
      throw new Error('导出失败');
    }
  } catch (error) {
    message.error('导出失败');
  }
}

export function getCurrentFormattedTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const commonPlaceHolder = (label: string) => `请输入${label}`;

export const requiredRule = (message: string) => ({
  required: true,
  message,
});

/**
 * 通用的 options 获取函数
 * @param fetchListFn 需要传入一个返回 Promise 的函数，且返回值结构需包含 code、msg、data.data
 * @param labelKey 作为 label 的字段名
 * @param valueKey 作为 value 的字段名
 * @returns Promise<Array<{label: string, value: string}>>
 */
export async function fetchSelectOptions(
  fetchListFn: () => Promise<any>,
  labelKey: string,
  valueKey: string,
  dataKey: string = '',
) {
  try {
    const res = await fetchListFn();
    if (res?.code === '200' || res.success === true) {
      let list = res?.data?.data || [];
      if (dataKey) list = res[dataKey];
      return list.map((item: any) => ({
        label: item[labelKey],
        value: item[valueKey],
      }));
    }
    message.error(res?.msg || '获取列表失败');
    return [];
  } catch (error) {
    message.error('获取列表异常，请稍后重试');
    return [];
  }
}

/**
 * 通用的树形下拉框 options 获取函数
 * @param fetchListFn 需要传入一个返回 Promise 的函数，且返回值结构需包含 code、msg、data.data
 * @param labelKey 作为 label 的字段名
 * @param valueKey 作为 value 的字段名
 * @param childrenKey 作为 children 的字段名，默认为 'children'
 * @param dataKey 数据主键，默认为空字符串
 * @returns Promise<Array<{label: string, value: string, children?: Array}>>
 */
export async function fetchTreeSelectOptions(
  fetchListFn: () => Promise<any>,
  labelKey: string,
  valueKey: string,
  childrenKey: string = 'children',
  dataKey: string = '',
) {
  try {
    const res = await fetchListFn();
    if (res?.code === '200' || res.success === true) {
      let list = res?.data?.data || [];
      console.log('list: ', list);
      if (dataKey) list = res[dataKey];
      // 递归处理树形结构
      const mapTree = (items: any[]): any[] => {
        return (items || []).map(item => {
          const node: any = {
            label: item[labelKey],
            value: item[valueKey],
          };
          if (
            item[childrenKey] &&
            Array.isArray(item[childrenKey]) &&
            item[childrenKey].length > 0
          ) {
            node.children = mapTree(item[childrenKey]);
          }
          return node;
        });
      };
      return mapTree(list);
    }
    message.error(res?.msg || '获取树形列表失败');
    return [];
  } catch (error) {
    message.error('获取树形列表异常，请稍后重试');
    return [];
  }
}

/**
 * 获取并处理级联下拉框数据
 *
 * @param fetchListFn    获取数据的异步函数，返回 Promise
 * @param labelKey       指定 label 字段名
 * @param valueKey       指定 value 字段名
 * @param childrenKey    指定 children 字段名
 * @returns              处理后的级联下拉框数据 [{ label, value, children }]
 *
 * 该函数会递归遍历原始数据，将其转换为适用于级联下拉框的数据结构。
 * 若接口请求失败或异常，会弹出错误提示并返回空数组。
 */
export async function fetchCascaderOptions(
  fetchListFn: () => Promise<any>,
  labelKey: string,
  valueKey: string,
  childrenKey: string,
) {
  // 递归处理数据结构
  function mapCascaderData(list: any[]): any[] {
    return (list || []).map(item => {
      const mappedItem: any = {
        label: item[labelKey],
        value: item[valueKey],
      };
      if (Array.isArray(item[childrenKey]) && item[childrenKey].length > 0) {
        mappedItem.children = mapCascaderData(item[childrenKey]);
      }
      return mappedItem;
    });
  }

  try {
    const res = await fetchListFn();
    if (res?.code === '200') {
      const list = res?.data || [];
      return mapCascaderData(list);
    }
    message.error(res?.msg || '获取级联列表失败');
    return [];
  } catch (error) {
    message.error('获取级联列表异常，请稍后重试');
    return [];
  }
}

export const getFileNameFromUrl = (url: string) => {
  if (!url) return '-';
  try {
    // 尝试从 query string 中获取 fileName
    const queryString = url.split('?')[1];
    if (queryString) {
      const params = new URLSearchParams(queryString);
      const fileName = params.get('fileName');
      if (fileName) return fileName;
    }
    // 没有 fileName 参数，走原有逻辑
    return url.split('/').pop() || '-';
  } catch (e) {
    return '-';
  }
};

export const dictConfigWrapper = (code: string) => {
  return fetchSelectOptions(
    () => sysDictList({ classCodes: code }),
    'dictValue',
    'dictCode',
    'data',
  );
};

export const defaultOptionValueWrapper = (label: string) => {
  return (options: any) => options.find((v: any) => v.label === label)?.value;
};

// 工具函数：判断 product 是否在 dValue 区间内
export function isProductInRange(product: number, dValue: string): boolean {
  // 统一替换全角符号为半角符号
  const normalized = dValue.replace(/＞/g, '>').replace(/＜/g, '<');
  if (normalized.startsWith('>')) {
    const min = Number(normalized.slice(1));
    return product > min;
  }
  if (normalized.startsWith('<')) {
    const max = Number(normalized.slice(1));
    return product < max;
  }
  if (normalized.includes('-')) {
    const [min, max] = normalized.split('-').map(Number);
    return product >= min && product <= max;
  }
  return false;
}
