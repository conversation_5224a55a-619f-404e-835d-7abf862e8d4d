import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';

const {
  nameLengthRules,
  codeLengthRules,
  codeRules,
  textAreaLengthRules,
  phoneRules,
  phoneLengthRules,
} = RULES;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['注册地址', 'registrationAddress', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['主要负责人', 'chiefPerson', 'Input', true],
  },
  {
    quickItemParams: ['联系方式', 'contactPhone', 'Input', true],
    rules: [phoneRules, phoneLengthRules],
  },
  {
    quickItemParams: ['预案编号', 'planNumber', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  {
    quickItemParams: ['备案日期', 'filingDate', 'DatePicker', true],
  },
  {
    quickItemParams: ['备案有效期', 'validityDate', 'DatePicker', true],
    rules: [
      ({ getFieldValue }: any) => ({
        validator(_: any, value: any) {
          const filingDate = getFieldValue('filingDate');
          if (value && filingDate && value <= filingDate) {
            return Promise.reject(new Error('备案有效期必须大于备案日期'));
          }
          return Promise.resolve();
        },
      }),
    ],
  },
  { name: 'enterpriseCode', type: 'hideItem', relatedValue: 'enterpriseName.enterpriseCode' },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
