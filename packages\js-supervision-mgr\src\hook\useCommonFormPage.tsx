import React, { useState } from 'react';
import { baseButtonStyle, EditPageTitle, ViewPageTitle } from '@/utils/constants';
import { FormOpenType, UploadButton } from 'jishan-components';
import { message, Modal } from 'antd';
import { exportFile } from '@/utils/commonFunction';

type DelService = (
  params: Record<
    'idParams',
    {
      id: number | string;
    }[]
  >,
) => Promise<any>;

type SaveService = (params: Record<string, any>) => Promise<any>;

type DetailService = (id: number) => Promise<any>;

export function useCommonFormPage<T = any>(options: { HomeTitle?: string }) {
  const { HomeTitle = '' } = options;

  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [initValue, setInitValue] = useState<T | Record<string, any>>({});
  const [openType, setOpenType] = useState<FormOpenType>('add');
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [id, setId] = useState('');

  const openFormWithAdd = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const saveHandler = async (
    formData: any,
    saveService: SaveService,
    dataHandle: (data: any) => any = data => data,
  ) => {
    let newFormData = { ...formData };
    if (openType === 'edit') {
      // 编辑时需要添加公司ID
      newFormData.id = id;
    }

    newFormData = dataHandle(newFormData);
    if (typeof newFormData === 'boolean' && !newFormData) return false;

    const res = await saveService(newFormData);
    if (res.code === '200') {
      message.success('保存成功！');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const deleteHandler = async (ids: string[] | number[], delService: DelService) => {
    if (ids.length === 0) {
      message.warning('请至少选择一条记录！');
      return;
    }
    Modal.confirm({
      title: '确认删除',
      content: '您确定要删除选中的消息吗？',
      onOk: async () => {
        const idParams = ids.map(id => ({ id }));
        const res = await delService({ idParams });
        if (res.code === '200') {
          message.success('删除成功！');
          setUpdateTrigger(!updateTrigger);
          setSelectedKeys([]);
        } else {
          message.error(res.msg);
        }
      },
    });
  };

  const selectedRowsDel = (delService: DelService) => {
    deleteHandler(selectedKeys, delService);
  };

  const editOrDetailHandler = async (
    record: any,
    type: 'view' | 'edit',
    title: string,
    service: DetailService,
    dataHandle: (data: any) => any = data => data,
  ) => {
    setFormLoading(true);
    setFormTitle(title);
    setOpenType(type);
    setFormVisible(true);
    const { id } = record;
    setId(id);
    try {
      const detailInfo = await service(id);
      const { code, data } = detailInfo;
      if (code === '200') {
        setInitValue(dataHandle(data));
      }
    } catch (error) {
      message.error('详情获取或解析失败，请稍后再试！');
    } finally {
      setFormLoading(false);
    }
  };

  const customEditPageHandle = (initValue: any) => {
    setInitValue(initValue);
    setFormVisible(true);
    setOpenType('edit');
    setFormTitle(EditPageTitle);
  };

  const editHandler = (
    record: any,
    service: DetailService,
    dataHandle: (data: any) => any = data => data,
    title: string = EditPageTitle,
  ) => {
    editOrDetailHandler(record, 'edit', title, service, dataHandle);
  };

  const detailHandler = (
    record: any,
    service: DetailService,
    dataHandle: (data: any) => any = data => data,
  ) => {
    editOrDetailHandler(record, 'view', ViewPageTitle, service, dataHandle);
  };

  // 使用该函数前注意将 setFormValues 传给 ListRequest
  const setExportEvent = (exportUrl: string, exportTitle: string) => {
    return () => {
      // 创建一个新对象，去除值为 undefined 的字段
      const filteredFormValues = Object.fromEntries(
        Object.entries(formValues).filter(([_, value]) => value !== undefined),
      );
      message.warning('正在导出，请勿重复点击！');
      exportFile(filteredFormValues, exportUrl, exportTitle);
    };
  };

  const onRowSelect = (selectedKeys: React.Key[]) => {
    setSelectedKeys(selectedKeys as string[]);
  };

  const getUploadButton = (url: string) => {
    return (
      <UploadButton
        buttonText="导入"
        onUploadSuccess={() => setUpdateTrigger(!updateTrigger)}
        onUploadError={() => setUpdateTrigger(!updateTrigger)}
        action={url}
        showUploadIcon={false}
        style={{ ...baseButtonStyle, border: 'none', paddingRight: 5 }}
      />
    );
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  return {
    editHandler,
    detailHandler,
    formTitle,
    setFormTitle,
    formVisible,
    setFormVisible,
    initValue,
    setInitValue,
    openType,
    setOpenType,
    updateTrigger,
    setUpdateTrigger,
    formLoading,
    setFormLoading,
    selectedKeys,
    setSelectedKeys,
    selectedRowsDel,
    id,
    setId,
    onRowSelect,
    customEditPageHandle,
    turnToListPage,
    deleteHandler,
    saveHandler,
    getUploadButton,
    openFormWithAdd,
    setFormValues,
    formValues,
    setExportEvent,
  };
}
