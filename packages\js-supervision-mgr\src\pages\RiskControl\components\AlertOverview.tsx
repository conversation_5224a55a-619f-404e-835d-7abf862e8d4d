import React, { useEffect, useState } from 'react';
import AlertInfoCard from './AlertInfoCard';
import '../index.less';

interface AlertOverviewProps {
  warningCount: any[];
}

interface AlertData {
  dictValue: string;
  totalCount: string;
}

interface WarningItem {
  name: string;
  data: AlertData[];
}

const AlertOverview: React.FC<AlertOverviewProps> = ({ warningCount }) => {
  const [alertCards, setAlertCards] = useState<any[]>([]);
  const [totalAlerts, setTotalAlerts] = useState<number>(0);

  useEffect(() => {
    if (Array.isArray(warningCount) && warningCount.length > 0) {
      // 动态处理每个预警类型
      const cards = (warningCount as WarningItem[]).map((item: WarningItem) => ({
        title: item.name,
        alerts: Array.isArray(item.data)
          ? item.data.map((d: AlertData) => ({
            title: d.dictValue,
            count: parseInt(d.totalCount, 10),
          }))
          : [],
      }));
      setAlertCards(cards);
      // 计算总数
      const total = cards.reduce(
        (sum, card) => sum + card.alerts.reduce((s, alert) => s + (alert.count || 0), 0),
        0,
      );
      setTotalAlerts(total);
    } else {
      setAlertCards([]);
      setTotalAlerts(0);
    }
  }, [warningCount]);

  return (
    <div className="alert-overview">
      <div className="alert-title">预警总览</div>
      <div className="alert-overview__left">
        <div className="alert-overview__total-title">预警总数</div>
        <div className="alert-overview__total-count">{totalAlerts}条</div>
      </div>
      <div className="alert-overview__right">
        {alertCards.map(card => (
          <AlertInfoCard key={card.title} title={card.title} alerts={card.alerts} />
        ))}
      </div>
    </div>
  );
};

export default AlertOverview;
