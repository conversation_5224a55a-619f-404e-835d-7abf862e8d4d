import React from 'react';
import { SearchInput, CommonLinkButton } from 'jishan-components';

// 表格列定义
export const tableColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    render: (_text: any, _record: any, index: number) => index + 1,
  },
  {
    title: '资质证书名称',
    dataIndex: 'certificateName',
    width: 200,
  },
  {
    title: '证书编号',
    dataIndex: 'certificateNumber',
    width: 200,
  },
  {
    title: '证书状态',
    dataIndex: 'status',
    width: 100,
    render: (status: string) => {
      const statusMap: Record<string, { text: string; color: string }> = {
        valid: { text: '有效', color: '#52c41a' },
        expired: { text: '过期', color: '#ff4d4f' },
        expiring: { text: '即将过期', color: '#faad14' },
      };
      const statusInfo = statusMap[status] || { text: status, color: '#666' };
      return <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>;
    },
  },
];

// 筛选条件
export const filterItems = [
  {
    name: 'certificateName',
    label: '证书名称',
    component: <SearchInput placeholder="请输入证书名称" />,
  },
  {
    name: 'certificateNumber',
    label: '证书编号',
    component: <SearchInput placeholder="请输入证书编号" />,
  },
];

// 模拟数据
export const mockData = {
  data: [
    {
      id: 1,
      certificateName: '危险化学品企业安全生产许可证',
      certificateNumber: '(晋) WH安许证 [2024] 235B2Y1号',
      status: 'expired',
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
    },
    {
      id: 2,
      certificateName: '危险化学品经营许可证',
      certificateNumber: '(晋) WH经许证 [2024] 235B2Y1号',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
    {
      id: 3,
      certificateName: '特种设备使用登记证',
      certificateNumber: '晋EG0142 (19)',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
    {
      id: 4,
      certificateName: '危险化学品安全使用许可证',
      certificateNumber: '晋运危化学字 [2019] 0001',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
    {
      id: 5,
      certificateName: '第一类非药类易制毒化学品生产许可证',
      certificateNumber: '(晋) 3500000001号',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
    {
      id: 6,
      certificateName: '第一类非药类易制毒化学品经营许可证',
      certificateNumber: '(晋) 3500000090号',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
    {
      id: 7,
      certificateName: '采矿许可证',
      certificateNumber: 'C1234567890',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
    {
      id: 8,
      certificateName: '营业执照',
      certificateNumber: '91140807198616450',
      status: 'valid',
      validFrom: '2024-01-01',
      validTo: '2025-12-31',
    },
  ],
  total: 8,
  pageNo: 1,
  pageSize: 10,
};

// 统计信息
export const getStatistics = (data: any[]) => {
  const total = data.length;
  const valid = data.filter(item => item.status === 'valid').length;
  const expired = data.filter(item => item.status === 'expired').length;

  return {
    total,
    valid,
    expired,
  };
};
