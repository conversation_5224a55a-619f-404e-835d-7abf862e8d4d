import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';

const { nameLengthRules, textAreaLengthRules } = RULES;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['父级行业', 'parentName', 'Input', true],
    disabled: true,
  },
  {
    quickItemParams: ['子行业名称', 'subName', 'Input', true],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['子行业编码', 'subCode', 'Input', false, '根据规则自动生成'],
    disabled: true,
  },
  {
    quickItemParams: ['描述', 'description', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['父级行业', 'parentId', 'hideItem', true],
  },
];

interface ChildIndustriesFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const ChildIndustriesForm: React.FC<ChildIndustriesFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default ChildIndustriesForm;
