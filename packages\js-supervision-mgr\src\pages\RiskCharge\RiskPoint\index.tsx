import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import {
  tableColumns,
  HomeTitle,
  filterItems,
  customButtons,
  exportTitle,
  exportUrl,
} from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { getriskTypeList } from '@/services/riskCharge/riskType';
import {
  deleteRiskPoint,
  getRiskPointDeatil,
  saveRiskPoint,
} from '@/services/riskCharge/riskPoint';
import { LevelContainer } from './components/LevelForm';
import { Modal } from 'antd';

const RiskPoint: React.FC = () => {
  const [columns, setColumns] = useState([...tableColumns]);
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [correctModalVisible, setCorrectModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    setFormValues,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    detailHandler,
    getUploadButton,
    selectedRowsDel,
    setExportEvent,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(setTableFilters, setColumns, {
    riskType: () => fetchSelectOptions(getriskTypeList, 'typeName', 'typeCode'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteRiskPoint);
  };

  customButtons[2].component = getUploadButton(`${SPPREFIX}/risk/point/importRiskPoint`);

  customButtons[3].onClick = setExportEvent(exportUrl, exportTitle);

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getRiskPointDeatil);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getRiskPointDeatil);
  };

  const openModal = (record: any) => {
    setCurrentRecord(record);
    setCorrectModalVisible(true);
  };

  const closeModal = () => {
    setCorrectModalVisible(false);
    setCurrentRecord(null);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
            <CommonLinkButton onClick={() => openModal(record)}>风险评级</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveRiskPoint)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/risk/point/riskPointList`}
        getFormValues={setFormValues}
        filterItems={[...tableFilters]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scrollYDelta={48}
        scroll={{ x: 1700 }}
      />
      <Modal
        title="风险评级"
        open={correctModalVisible}
        onCancel={closeModal}
        footer={null}
        width={1200}
        bodyProps={{ style: { height: '1000px' } }}
        destroyOnHidden
      >
        {currentRecord && <LevelContainer record={currentRecord} onClose={closeModal} />}
      </Modal>
    </CommonPage>
  );
};

export default RiskPoint;
