import { Upload, Button, UploadProps, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

export interface ImportButtonProps extends UploadProps {
  buttonText?: string;
  showUploadIcon?: boolean;
  onUploadSuccess?: (file: any, response: any) => void; // 上传成功回调
  onUploadError?: (error: any) => void; // 上传失败回调
}

const UploadButton: React.FC<ImportButtonProps> = ({
  buttonText,
  customRequest,
  onUploadError,
  onUploadSuccess,
  showUploadIcon = true,
  ...props
}) => {
  const [loading, setLoading] = useState(false);

  const uploadProps = {
    action: props.action,
    headers: {
      access_token: localStorage.getItem('jishan_token') || '',
    },
    name: 'files',
    showUploadList: false,
    onChange: ({ file }: any) => {
      switch (file.status) {
        case 'done': {
          const { code, msg } = file.response || {};
          if (code !== '200') {
            onUploadError?.(file.error);
            message.error(msg || '文件上传失败');
            setLoading(false);
            break;
          }
          onUploadSuccess?.(file, file.response);
          setLoading(false);
          break;
        }
        case 'error': {
          onUploadError?.(file.error);
          message.error('文件上传失败');
          setLoading(false);
          break;
        }
        case 'uploading': {
          setLoading(true);
          break;
        }
        default: {
          setLoading(false);
          break;
        }
      }
    },
  };

  return (
    <Upload {...props} {...uploadProps}>
      <Button
        icon={showUploadIcon ? <UploadOutlined /> : ''}
        style={props.style}
        loading={loading}
        disabled={loading}
      >
        {buttonText}
      </Button>
    </Upload>
  );
};

export default UploadButton;
