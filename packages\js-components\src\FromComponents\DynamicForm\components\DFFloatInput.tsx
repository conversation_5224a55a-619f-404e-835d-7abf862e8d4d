import { Input } from 'antd';
import React from 'react';
import { DynamicFormItem } from '../interface';

interface DFFloatInputProps {
  value?: string | number;
  onChange?: (value: string) => void;
  placeholder?: string;
  item: DynamicFormItem;
}

const DFFloatInput: React.FC<DFFloatInputProps> = ({ item, value, onChange, placeholder }) => {
  const decimalPlaces = item?.decimalPlaces;
  // 只允许输入数字、小数点和负号，并限制小数位数
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let val = e.target.value.replace(/[^0-9.-]/g, '');
    // 只允许一个小数点和一个负号
    val = val
      .replace(/(\..*)\./g, '$1') // 只允许一个小数点
      .replace(/(?!^)-/g, ''); // 负号只能在开头
    // 限制小数位数
    if (decimalPlaces !== undefined && val.includes('.')) {
      const [intPart, decPart] = val.split('.');
      val = `${intPart}.${decPart.slice(0, decimalPlaces)}`;
    }
    if (onChange) {
      onChange(val);
    }
  };

  return (
    <Input
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      type="text"
      autoComplete="off"
    />
  );
};

export default DFFloatInput;
