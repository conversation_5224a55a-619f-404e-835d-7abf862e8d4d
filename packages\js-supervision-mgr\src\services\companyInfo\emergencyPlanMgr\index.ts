import { request } from '@/utils/net';

export async function savePlan(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/contingencyPlan/savePlan`, params);
}

export async function getPlanDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/contingencyPlan/planDetail`, {
    id,
  });
}

export async function deletePlan(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/contingencyPlan/removePlan`, params);
}
