import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import {
  deletePositionInfo,
  getPositionDetail,
  savePosition,
} from '@/services/positionRisk/positionList';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { dictConfigWrapper } from '@/utils/commonFunction';

const CheckList: React.FC = () => {
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [columns, setColumns] = useState([...tableColumns]);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    setFormValues,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    selectedRowsDel,
    detailHandler,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(setTableFilters, setColumns, {
    positionCategory: () => dictConfigWrapper('position_category'),
    positionLevel: () => dictConfigWrapper('position_level'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deletePositionInfo);
  };

  const openEdit = (record: any) => {
    editHandler(record, getPositionDetail);
  };

  const openDetail = (record: any) => {
    detailHandler(record, getPositionDetail);
  };

  const handleBusinessTermTime = (params: any) => {
    const { filingDate, ...rest } = params;
    let startTime;
    let endTime;
    if (Array.isArray(filingDate) && filingDate.length === 2) {
      startTime = filingDate[0]?.format('YYYY-MM-DD');
      endTime = filingDate[1]?.format('YYYY-MM-DD');
    }
    return {
      ...rest,
      startTime,
      endTime,
    };
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, savePosition)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/riskCheck/inventory/checkList`}
        getFormValues={setFormValues}
        filterItems={[...tableFilters]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        searchParamsHandle={handleBusinessTermTime}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scroll={{ x: 1800 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default CheckList;
