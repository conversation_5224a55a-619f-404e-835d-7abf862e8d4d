import React from 'react';
import { SearchInput, JSCUtils } from 'jishan-components';

const { setOption } = JSCUtils;

export const EditPageTitle = '编辑';

export const ViewPageTitle = '详情';

export const companyModalSelectParams = {
  apiUrl: `${SPPREFIX}/basicinformation/enterpriseInformation/infoList`,
  filterItems: [
    {
      name: 'enterpriseName',
      label: '企业名称',
      component: <SearchInput placeholder="请输入企业名称" />,
    },
  ],
  width: 700,
  rowSelectType: 'radio' as any,
  valueName: 'enterpriseName',
  columns: [
    { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true, width: 250 },
    { title: '统一社会信用代码', dataIndex: 'enterpriseCode', width: 200 },
  ],
};

export const defaultRadioOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];

export const defaultGenderOptions = [
  { label: '男', value: 'M' },
  { label: '女', value: 'F' },
];

export const baseButtonStyle = {
  color: '#fff',
  backgroundColor: '#3164F6',
};

export const defaultImportBtnStyle = {
  ...baseButtonStyle,
  border: 'none',
  paddingRight: 4,
  marginRight: 4,
};

export const educationOptions = [
  setOption('小学'),
  setOption('普通高中'),
  setOption('中专'),
  setOption('职高'),
  setOption('技校'),
  setOption('大专'),
  setOption('本科'),
  setOption('硕士研究生'),
  setOption('博士研究生'),
];
