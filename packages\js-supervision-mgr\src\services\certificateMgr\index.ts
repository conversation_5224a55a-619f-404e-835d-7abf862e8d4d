import { request } from '@/utils/net';

export async function saveCertificate(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/certificateInfo/saveCertificate`, params);
}

export async function getCertificateDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/certificateInfo/certificateDetail`, {
    id,
  });
}

export async function deleteCertificate(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/certificateInfo/removeCertificate`, params);
}
