import CryptoJS from 'crypto-js';
import { request } from 'umi';

export async function loginByPasswod(params?: any, options?: { [key: string]: any }) {
  const { userName, password } = params;
  const key = CryptoJS.enc.Utf8.parse(AES_KEY);
  const cipher = CryptoJS.AES.encrypt(password, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  const passwordScreat = cipher.ciphertext.toString(CryptoJS.enc.Base64);
  return request<Record<string, any>>('/jssso/login/loginWithPassword', {
    method: 'POST',
    data: {
      clientId: CLIENT_ID,
      userName,
      service: SERVICE,
      password: passwordScreat,
    },
    ...(options || {}),
  });
}
export async function loginWithPhone(params?: any, options?: { [key: string]: any }) {
  const { phone } = params;
  const key = CryptoJS.enc.Utf8.parse(AES_KEY);
  const cipher = CryptoJS.AES.encrypt(phone, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  const passwordScreat = cipher.ciphertext.toString(CryptoJS.enc.Base64);
  return request<Record<string, any>>('/jssso/login/loginWithPhone', {
    method: 'POST',
    data: {
      clientId: CLIENT_ID,
      service: SERVICE,
      phone: passwordScreat,
    },
    ...(options || {}),
  });
}

export async function getUserMenuPermRole(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${portalPrefix}/api/v1/account/menuPermRole`, {
    method: 'GET',
    params: { systemCode: SYSTEM_CODE },
    ...(options || {}),
  });
}
export async function getSecurityCert(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/api/security/service-cert`, {
    method: 'GET',
    ...(options || {}),
  });
}
export async function validClientCert(data?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/api/security/valid-client-cert`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

export async function getSysRoles(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/api/v1/mgmt/permission/rolesBySysCode`, {
    method: 'GET',
    params: { sysCode: SYSTEM_CODE },
    ...(options || {}),
  });
}

export async function getOrgTreeList(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${portalPrefix}/api/v1/mgmt/org/tree`, {
    method: 'GET',
    ...(options || {}),
  });
}

export async function getAllSystem(options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${portalPrefix}/api/v1/account/systems?type=web`, {
    method: 'GET',
    ...(options || {}),
  });
}
