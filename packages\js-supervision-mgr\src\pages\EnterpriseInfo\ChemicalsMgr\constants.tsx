import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '重点化学品信息';

export const filterItems = [
  {
    name: 'chemicalName',
    label: '危化品名称',
    component: <SearchInput placeholder="请输入危化品名称" />,
  },
  {
    name: 'classification',
    label: '危化品类型',
    component: <SearchInput placeholder="请输入危化品类型" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '危化品名称', dataIndex: 'chemicalName', ellipsis: true, width: 300 },
  { title: 'cas号', dataIndex: 'casNumber', width: 350 },
  { title: '危化品类型', dataIndex: 'classification', width: 280 },
];
