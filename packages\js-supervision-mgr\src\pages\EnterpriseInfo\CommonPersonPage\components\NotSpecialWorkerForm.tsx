import React from 'react';
import { DynamicForm, DynamicFormItem, FormOpenType, SectionTitle } from 'jishan-components';
import imagesImg from '@/assets/images/common/image.png';

const formItems: DynamicFormItem[] = [
  { quickItemParams: ['证书名称', 'certificateName', 'Input', false] },
  { quickItemParams: ['证书编号', 'certificateNumber', 'Input', false] },
  { quickItemParams: ['有效日期', 'validityPeriod', 'DatePicker', false] },
  { quickItemParams: ['附件', 'attachment', 'ImgUpload', false], imgInfo: { imagesImg } },
];

export const notSpecialWorkerFormNames = formItems.map(item => item.name);

export const dateNames = ['validityPeriod'];

interface NotSpecialWorkerFormProps {
  cardIndex: number;
  openType: FormOpenType;
}

const NotSpecialWorkerForm: React.FC<NotSpecialWorkerFormProps> = ({ cardIndex, openType }) => {
  return (
    <>
      <SectionTitle title="人员资质信息" />
      <DynamicForm items={formItems} openType={openType} nameSuffix={cardIndex} />
    </>
  );
};

export default NotSpecialWorkerForm;
