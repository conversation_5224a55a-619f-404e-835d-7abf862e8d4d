import React from 'react';
import { Radio, Form, Col, FormInstance } from 'antd';
import { DynamicForm, SectionTitle, FormOpenType } from 'jishan-components';
import { formItems } from '../../CommonPersonPage/constants';
import { companyModalSelectParams, defaultRadioOptions } from '@/utils/constants';

const newFormItems = [...formItems];
newFormItems.splice(1, 0, {
  quickItemParams: ['外包公司单位', 'outsourcingCompany', 'ModalSelect', true],
  modalSelectInfo: companyModalSelectParams,
});
const positionItem = newFormItems.find(item => item.quickItemParams?.[1] === 'position') || {};
if (positionItem && positionItem.quickItemParams) {
  // 修改类型为 Input
  positionItem.quickItemParams[2] = 'Input';
}

interface BaseInfoFormProps {
  form?: FormInstance<any>;
  openType: FormOpenType;
  isSpecialWorker: string;
  onSpecialWorkerChange: (e: any) => void;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({
  form,
  openType,
  isSpecialWorker,
  onSpecialWorkerChange,
}) => {
  return (
    <>
      <SectionTitle title="人员基础信息" />
      <DynamicForm items={newFormItems} openType={openType} form={form} />
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="特殊作业人员"
          name="isSpecialWorker"
          initialValue="0"
        >
          <Radio.Group
            value={isSpecialWorker}
            options={defaultRadioOptions}
            onChange={onSpecialWorkerChange}
          />
        </Form.Item>
      </Col>
    </>
  );
};

export default BaseInfoForm;
