import { BellOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { SelectLang as UmiSelectLang } from '@umijs/max';
import { Drawer } from 'antd';
import React from 'react';
import MsgList from './MsgList';

export type SiderTheme = 'light' | 'dark';

export const SelectLang = () => {
  return (
    <UmiSelectLang
      style={{
        padding: 4,
      }}
    />
  );
};

export const Question = () => {
  return (
    <div
      style={{
        display: 'flex',
        height: 26,
      }}
      onClick={() => {
        window.open('https://pro.ant.design/docs/getting-started');
      }}
    >
      <QuestionCircleOutlined />
    </div>
  );
};

export const Notification = () => {
  const [visible, setVisible] = React.useState(false);
  const [msgCount, setMsgCount] = React.useState(0);
  const showDrawer = () => {
    setVisible(true);
  };
  const onClose = () => {
    setVisible(false);
  };

  const updateMessageCount = (num: number) => setMsgCount(num);
  return (
    <div>
      <div
        style={{
          display: 'flex',
          height: 26,
          cursor: 'pointer',
        }}
        onClick={showDrawer}
      >
        <BellOutlined />
      </div>
      <Drawer
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>未读消息{msgCount > 0 ? `(${msgCount})` : ''}</span>
            <a
              href="/jsalarm/msgMgr"
              style={{ marginLeft: 'auto', fontSize: '12px', fontWeight: 'normal' }}
            >
              更多消息
            </a>
          </div>
        }
        placement="right"
        onClose={onClose}
        open={visible}
      >
        <MsgList updateMessageCount={updateMessageCount} />
      </Drawer>
    </div>
  );
};

export default Question;
