import React from 'react';
import { DynamicForm, DynamicFormItem, FormOpenType, RULES } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';
import { riskModalSelectParams } from '../constants';

const { codeRules, codeLengthRules, textAreaLengthRules, phoneRules, phoneLengthRules } = RULES;

export const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
  },
  {
    quickItemParams: ['统一社会信用代码', 'enterpriseCode', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  {
    quickItemParams: ['风险点名称', 'riskName', 'ModalSelect', true],
    modalSelectInfo: riskModalSelectParams,
  },
  {
    quickItemParams: ['风险点编号', 'riskCode', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  { quickItemParams: ['风险分析', 'riskAnalysis', 'TextArea', true], rules: [textAreaLengthRules] },
  {
    quickItemParams: ['危害后果', 'hazardConsequence', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['管控措施', 'controlMeasures', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  { quickItemParams: ['风险等级', 'riskLevel', 'Input', true] },
  { quickItemParams: ['风险管控层级', 'controlLevel', 'Input', true] },
  { quickItemParams: ['上次评估日期', 'lastEvaluationDate', 'DatePicker', false] },
  {
    quickItemParams: ['落实情况', 'implementationStatus', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['相关资料', 'relatedMaterials', 'FileUpload', false],
    uploadFileInfo: { tip: '上传的附件大小不允许超过10M', maxFileSize: 10 },
  },
  { quickItemParams: ['责任单位', 'responsibleUnit', 'Input', true] },
  { quickItemParams: ['责任人', 'responsiblePerson', 'Input', true] },
  {
    quickItemParams: ['责任人电话', 'responsiblePhone', 'Input', true],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['审核人', 'reviewer', 'Input', true] },
  { quickItemParams: ['审核日期', 'reviewDate', 'DatePicker', true] },
  { quickItemParams: ['复评日期', 'reevaluationDate', 'DatePicker', false] },
  { type: 'hideItem', name: 'enterpriseCode', relatedValue: 'enterpriseName.enterpriseCode' },
  { type: 'hideItem', name: 'riskCode', relatedValue: 'riskName.riskCode' },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

export const fileNames = ['relatedMaterials'];

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return <DynamicForm items={formItems} openType={openType} form={form} />;
};

export default BaseInfoForm;
