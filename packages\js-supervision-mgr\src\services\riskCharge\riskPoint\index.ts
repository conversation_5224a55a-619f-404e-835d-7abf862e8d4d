import { request } from '@/utils/net';

export async function saveRiskPoint(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/risk/point/saveRiskPoint`, params);
}

export async function getRiskPointDeatil(id: number) {
  return request.get(`${SPPREFIX}/risk/point/riskPointDeatil`, {
    id,
  });
}

export async function deleteRiskPoint(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/risk/point/removeRiskPoint`, params);
}

export async function getLevelFieldsOptions(params: Record<string, any>) {
  return request.get(`${SPPREFIX}/riskLevel/scaleList/riskMatchList`, {
    pageNo: 1,
    pageSize: 1000,
    ...params,
  });
}
