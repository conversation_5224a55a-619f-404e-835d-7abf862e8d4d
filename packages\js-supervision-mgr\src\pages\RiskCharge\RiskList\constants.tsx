import React from 'react';
import { CustomButton, SearchInput, JSCUtils } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

const { getTemplateDownloadUrl } = JSCUtils;

export const HomeTitle = '风险清单';

export const exportTitle = '风险清单';

export const exportUrl = `${SPPREFIX}/risk/list/exportRiskList`;

export const filterItems = [
  {
    name: 'riskName',
    label: '风险点名称',
    component: <SearchInput placeholder="请输入风险点名称" />,
  },
  {
    name: 'riskType',
    label: '风险类型',
    component: <SearchInput placeholder="请输入风险类型" />,
    span: 5,
  },
  {
    name: 'riskLevel',
    label: '风险等级',
    component: <SearchInput placeholder="请输入风险等级" />,
  },
];

const urlArray: [string, string] = [
  `${HomeTitle}.xlsx`,
  '20250729/fa0eacfd-a1cb-416f-9b34-0466281039a3.xlsx',
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '导入', ...baseButtonStyle },
  { text: '导出', ...baseButtonStyle },
  {
    text: '模板下载',
    ...baseButtonStyle,
    onClick: () => window.open(getTemplateDownloadUrl(...urlArray), '_blank'),
  },
];

export const tableColumns = [
  { title: '风险点名称', dataIndex: 'riskName', width: 150 },
  { title: '风险类型', dataIndex: 'riskType', width: 120 },
  { title: '风险级别', dataIndex: 'riskLevel', width: 100 },
  { title: '风险因子', dataIndex: 'riskFactor', width: 150 },
  { title: '风险负责人', dataIndex: 'safetyPrincipal', width: 120 },
  { title: '联系方式', dataIndex: 'principalContact', width: 120 },
  { title: '后果', dataIndex: 'mainConsequence', width: 200 },
  { title: '应急处置建议', dataIndex: 'emergencyMeasures', width: 200 },
  { title: '详细地址', dataIndex: 'riskLocation', width: 180 },
];
