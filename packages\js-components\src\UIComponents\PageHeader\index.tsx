import React from 'react';

interface PageHeaderProps {
  title: string;
  onClick: () => void;
  showBackIcon?: boolean;
  backIcon?: string; // 新增：由父组件传入
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  onClick,
  showBackIcon = false,
  backIcon, // 新增
}) => {
  return (
    <div className="common-page-header">
      {showBackIcon ? (
        <div className="add-header">
          <img src={backIcon ?? ''} alt="back-icon" onClick={onClick} />
          <span>{title}</span>
        </div>
      ) : (
        title
      )}
    </div>
  );
};
export default PageHeader;
