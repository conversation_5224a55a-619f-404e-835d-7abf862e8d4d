import React, { useState, useEffect } from 'react';
import { FormInstance, Input, message } from 'antd';
import ModalListRequest from '../../../ListComponents/ModalListRequest';
import { DynamicFormItem } from '../interface';
import { getValueByPath } from '../../../utils/commonFunction';

interface EnterpriseSelectProps {
  value?: any; // 选中的企业对象或ID
  onChange?: (value: any) => void;
  placeholder?: string;
  item: DynamicFormItem;
  formStoreHandle?: (item: DynamicFormItem, option: any) => void;
  form?: FormInstance<any>;
}

const DFModalSelect: React.FC<EnterpriseSelectProps> = ({
  item,
  value,
  onChange,
  placeholder = '请选择企业',
  formStoreHandle,
  form,
}) => {
  const {
    apiUrl = '',
    pageSize = 5,
    method = 'GET',
    rowSelectType = 'radio',
    inlineButtons = true,
    labelCol = { span: 8 },
    width,
    valueName,
    relatedSearchParams = [],
    ...restParams
  } = item?.modalSelectInfo || { filterItems: [], columns: [] };

  const [modalVisible, setModalVisible] = useState(false);
  const [selected, setSelected] = useState<any>(value);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  // 当外部 value 变化时，同步内部 selected
  useEffect(() => {
    setSelected(value);
  }, [value]);

  // 选择后回调
  const handleRowSelect = (keys: any[], rows: any) => {
    if (keys && keys.length > 0) {
      setSelected(keys);
      setSelectedRows(rows);
    }
  };

  const handleSearchParams = (params: any) => {
    const newParams = { ...params };
    if (form) {
      relatedSearchParams.forEach(field => {
        // 从 form 中获取对应字段的值
        const fieldValue = form.getFieldValue(field);
        // 赋值到 newParams
        newParams[field] = fieldValue;
      });
    }
    return newParams;
  };

  const handleConfim = () => {
    if (!selected || selected.length === 0) {
      message.warning('请至少选择一项');
      return;
    }
    if (rowSelectType === 'radio') {
      // 单选
      const row = selectedRows[0];
      let resultValue: any = selected[0];
      if (valueName) {
        if (typeof valueName === 'string' && valueName.includes('.')) {
          resultValue = getValueByPath(row, valueName);
        } else if (typeof valueName === 'string') {
          resultValue = row ? row[valueName] : undefined;
        }
        if (resultValue === undefined) {
          resultValue = selected[0];
        }
      }
      onChange?.(resultValue);
      formStoreHandle?.(item, row);
    } else if (rowSelectType === 'checkbox') {
      // 多选，暂时只留口子
      // TODO: 后续补充多选时的回传逻辑
      // 例如：onChange?.(selectedRows.map(row => ...))
      message.info('多选回传逻辑待补充');
    }
    setModalVisible(false);
    setSelectedRows([]);
  };

  return (
    <>
      <Input
        readOnly
        value={value}
        placeholder={placeholder}
        onClick={() => setModalVisible(true)}
      />
      <ModalListRequest
        rowSelectType={rowSelectType}
        width={width || 850}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onConfirm={handleConfim}
        apiUrl={apiUrl} // 这里替换成你的实际接口
        pageSize={pageSize}
        rowSelection
        method={method}
        labelCol={labelCol}
        inlineButtons={inlineButtons}
        onRowSelect={handleRowSelect}
        searchParamsHandle={handleSearchParams}
        {...restParams}
      />
    </>
  );
};

export default DFModalSelect;
