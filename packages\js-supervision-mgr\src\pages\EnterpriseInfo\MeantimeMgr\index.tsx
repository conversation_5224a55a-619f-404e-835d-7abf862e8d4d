import React from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm, { fileNames } from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import {
  deleteMeantime,
  getMeantimeDetail,
  saveMeantime,
} from '@/services/companyInfo/meantimeMgr';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const MeantimeMgr: React.FC = () => {
  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    selectedKeys,
    formLoading,
    openFormWithAdd,
    deleteHandler,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    detailHandler,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = async () => {
    deleteHandler(selectedKeys, deleteMeantime);
  };

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getMeantimeDetail);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getMeantimeDetail);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveMeantime)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        normalFileNames: fileNames,
        fileStringFormat: true,
      }}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/threemeantime/meantimeList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default MeantimeMgr;
