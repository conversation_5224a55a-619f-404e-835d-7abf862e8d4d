import React from 'react';
import { Dropdown, Button } from 'antd';
import { UploadButton } from 'jishan-components';

interface ImportDropdownButtonProps {
  onUploadFinish?: () => void;
  setUpdateTrigger: React.Dispatch<React.SetStateAction<boolean>>;
}

const ImportDropdownButton: React.FC<ImportDropdownButtonProps> = ({
  onUploadFinish,
  setUpdateTrigger,
}) => {
  const handleUploadFinish = () => {
    setUpdateTrigger(prev => !prev);
    onUploadFinish?.();
  };

  return (
    <Dropdown
      menu={{
        items: [
          {
            key: 'type1',
            label: (
              <UploadButton
                buttonText="非特殊作业人员"
                onUploadSuccess={handleUploadFinish}
                onUploadError={handleUploadFinish}
                showUploadIcon={false}
                action={`${SPPREFIX}/basicinformation/personMsg/importNonSpecialPerson`}
              />
            ),
          },
          {
            key: 'type2',
            label: (
              <UploadButton
                buttonText="&nbsp;&nbsp;特殊作业人员&nbsp;&nbsp;"
                onUploadSuccess={handleUploadFinish}
                onUploadError={handleUploadFinish}
                showUploadIcon={false}
                action={`${SPPREFIX}/basicinformation/personMsg/importSpecialPerson`}
              />
            ),
          },
        ],
      }}
      trigger={['click']}
    >
      <Button type="primary" style={{ marginRight: 8 }}>
        导入
      </Button>
    </Dropdown>
  );
};

export default ImportDropdownButton;
