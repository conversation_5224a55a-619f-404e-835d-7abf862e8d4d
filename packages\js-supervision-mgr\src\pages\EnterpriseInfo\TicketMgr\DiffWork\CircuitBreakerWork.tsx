import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  OpinionItem,
} from '../constants';
import { DynamicFormItem, SearchInput, RULES } from 'jishan-components';
import { getOpinionItems, setSignTimeWrapper } from '../utils';
import SafetyMeasuresList from '../components/Measures';

const HomeTitle = '断路作业';

const { textAreaLengthRules } = RULES;

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  { title: '断路原因', dataIndex: 'openCircuitCause', width: 220 },
  { title: '涉及相关单位（部门）', dataIndex: 'relevantUnits', width: 220 },
);

const reasonItem = {
  name: 'openCircuitCause',
  label: '断路原因',
  component: <SearchInput placeholder="请输入断路原因" />,
};

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.workUnit,
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  { quickItemParams: ['涉及相关单位(部门)', 'relevantUnits', 'Input', true] },
  FrequencyItems.supervisor,
  { quickItemParams: ['断路原因', 'openCircuitCause', 'Input', true] },
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  FrequencyItems.actualTime,
  { type: 'blank' },
  {
    quickItemParams: ['断路地段示意图（可另附图）及相关说明', 'scopeWork', 'TextArea', true],
    rules: [textAreaLengthRules],
    span: 16,
  },
  {
    quickItemParams: ['', 'scopeWorkFile', 'FileUpload', true, '请上传图片'],
    span: 3,
    uploadFileInfo: {
      tip: '只能上传jpg、png格式',
      targetType: '.png,.jpg',
      buttonTitle: '点击上传图片',
    },
  },
  {
    quickItemParams: ['', 'scopeWorkSignature', 'Sign', true, '请签名'],
    span: 4,
    signInfo: { signCallback: setSignTimeWrapper('scopeWorkTime') },
  },
  { type: 'hideItem', name: 'scopeWorkTime' },
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  ...OpinionItem.jobOpinion,
  ...OpinionItem.unitOpinion,
  ...getOpinionItems('消防、安全管理部门意见', 'department', { labelCol: { span: 6 } }),
  ...getOpinionItems('审批部门意见', 'openFire'),
  ...OpinionItem.completedOpinion,

  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'blindDrawingName' },
];

const CircuitBreakerWork: React.FC = () => {
  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[reasonItem, applyTimeItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      keyName={HomeTitle}
      formItems={formItems}
      imgNames={['scopeWorkFile']}
      x={1400}
    />
  );
};

export default CircuitBreakerWork;
