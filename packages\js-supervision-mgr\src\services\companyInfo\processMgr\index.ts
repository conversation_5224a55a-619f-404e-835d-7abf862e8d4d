import { request } from '@/utils/net';

export async function saveProcess(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/productionProcess/saveProcess`, params);
}

export async function getProcessDeatil(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/productionProcess/processDeatil`, {
    id,
  });
}

export async function deleteProcess(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/productionProcess/removeProcess`, params);
}
