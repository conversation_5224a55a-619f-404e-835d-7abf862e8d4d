import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  OpinionItem,
  getLocationColumn,
} from '../constants';
import { DynamicFormItem, SearchInput } from 'jishan-components';
import { getOpinionItems } from '../utils';
import SafetyMeasuresList from '../components/Measures';

const HomeTitle = '爆破作业';

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  { title: '作业内容', dataIndex: 'workContent', width: 220 },
  getLocationColumn('作业地点'),
);

const contentItem = {
  name: 'workContent',
  label: '作业内容',
  component: <SearchInput placeholder="请输入作业内容" />,
};

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.workContent,
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  FrequencyItems.workPlace,
  { quickItemParams: ['施工现场负责人', 'buildSiteName', 'Input', true] },
  { quickItemParams: ['施工安全负责人', 'buildSafetyName', 'Input', true] },
  FrequencyItems.actualTime,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  ...OpinionItem.jobOpinion,
  ...getOpinionItems('工程部意见', 'engineer'),
  ...getOpinionItems('设备物资部意见', 'facility'),
  ...getOpinionItems('安全部意见', 'safety'),
  ...getOpinionItems('安全总监意见', 'safetyDirector'),
  ...OpinionItem.completedOpinion,

  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'blindDrawingName' },
];

const BlastingWork: React.FC = () => {
  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[contentItem, applyTimeItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      keyName={HomeTitle}
      formItems={formItems}
    />
  );
};

export default BlastingWork;
