import React from 'react';
import { CustomButton, FixedWidthRangePicker, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '资质证照维护';

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入企业名称" />,
  },
  {
    name: 'certificateName',
    label: '证件名称',
    component: <SearchInput placeholder="请输入证件名称" />,
  },
  {
    name: 'businessTermDate',
    label: '经营期限',
    component: <FixedWidthRangePicker />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true, width: 200 },
  { title: '证件名称', dataIndex: 'certificateName', width: 150 },
  { title: '证件编号', dataIndex: 'certificateNo', width: 180 },
  { title: '发证日期', dataIndex: 'issuingAuthorityDate', width: 130 },
  { title: '发证机构', dataIndex: 'issuingAuthority', width: 200 },
  {
    title: '有效期',
    dataIndex: 'businessTerm',
    width: 200,
    render: (_: any, record: any) => {
      const { validityStart, validityEnd } = record;
      if (!validityEnd) {
        return '无固定期限';
      }
      return `${validityStart || ''} ~ ${validityEnd}`;
    },
  },
];
