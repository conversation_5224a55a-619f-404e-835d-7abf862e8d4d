import React from 'react';
import UploadCustom from '../../../BaseFormComponents/UploadCustom';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { DynamicFormItem } from '../interface';
import { FormOpenType } from '../../FormContainer';
import { getCompName } from '../../../utils/commonFunction';
import { getFileList, FileUploadValue } from '../../../utils/form';
import './components.less';

interface DFFileUploadProps {
  value?: FileUploadValue; // 图片路径
  onChange?: (value: string) => void;
  openType: FormOpenType;
  item: DynamicFormItem;
}

const DFFileUpload: React.FC<DFFileUploadProps> = ({ value, onChange, openType, item }) => {
  const {
    emptyText = '未上传附件',
    tip = '',
    maxNum = 1,
    maxFileSize,
    targetType = '',
    buttonTitle,
  } = item?.uploadFileInfo || {};

  const valueList = getFileList(value);

  // 没有图片时显示空态
  if (!valueList.length && openType === 'view') {
    return (
      <div className={getCompName('empty')}>
        <ExclamationCircleOutlined className={getCompName('empty-icon')} />
        <span>{emptyText}</span>
      </div>
    );
  }

  return (
    <>
      <UploadCustom
        type="docMng"
        value={valueList}
        maxSize={maxFileSize}
        maxNum={maxNum}
        onChange={onChange}
        targetType={targetType}
        buttonTitle={buttonTitle}
      />
      {tip && (
        <div
          className={getCompName('file-upload-tip')}
          style={{ color: '#999', fontSize: 12, marginTop: 4 }}
        >
          {tip}
        </div>
      )}
    </>
  );
};

export default DFFileUpload;
