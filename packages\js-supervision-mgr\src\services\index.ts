import { request } from 'umi';

// 查询字典
export async function getEventCodeDict(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/common/getEventCodeDict`, {
    method: 'GET',
    params: { ...params },
    ...(options || {}),
  });
}

// 查询字典目录
export async function getEventCatg(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/common/getEventCatg`, {
    method: 'GET',
    params: { ...params },
    ...(options || {}),
  });
}

// 批量删除文件
export async function batchDeletFile(params?: any, options?: { [key: string]: any }) {
  // 基础 URL
  let url = `${PREFIX_SERVER}/api/batchDelet`;

  // 添加参数
  if (params) {
    const queryParts = [];
    if (params.bucket) {
      queryParts.push(`bucket=${encodeURIComponent(params.bucket)}`);
    }
    if (params.objectNames && Array.isArray(params.objectNames)) {
      params.objectNames.forEach((objectName: any) => {
        queryParts.push(`objectNames=${encodeURIComponent(objectName)}`);
      });
    }
    // 拼接查询字符串
    url += `?${queryParts.join('&')}`;
  }
  return request<Record<string, any>>(url, {
    method: 'GET',
    ...(options || {}),
  });
}
