import React, { useState, useMemo } from 'react';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import { Checkbox } from 'antd';
import {
  tableColumns,
  filterItems,
  mockData,
  getStatistics,
  certificateStatusOptions,
  personnelTypeOptions,
} from './constants';
import StatisticsDescription from './StatisticsDescription';
import PersonnelDetailModal from './PersonnelDetailModal';
import './index.less';

interface PersonnelProps {
  data?: any;
}

const Personnel: React.FC<PersonnelProps> = () => {
  const [updateTrigger] = useState(0);
  const [columns] = useState([...tableColumns]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);

  // 模拟 API 请求处理
  const serviceResHandle = (_res: any) => {
    // 在实际项目中，这里会处理真实的 API 响应
    // 现在返回模拟数据
    return {
      data: mockData.data,
      totalRecords: mockData.total,
    };
  };

  // 计算统计信息
  const statistics = useMemo(() => {
    return getStatistics(mockData.data);
  }, []);

  // 打开详情弹框
  const openDetail = (record: any) => {
    setSelectedRecord(record);
    setModalVisible(true);
  };

  // 关闭弹框
  const handleModalClose = () => {
    setModalVisible(false);
    setSelectedRecord(null);
  };

  // 添加操作列
  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      width: 100,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openDetail(record)}>查看</CommonLinkButton>
        </div>
      ),
    },
  ];

  // 扩展筛选项，添加证书状态和人员分类的复选框
  const extendedFilterItems = [
    ...filterItems,
    {
      name: 'certificateStatus',
      label: '证书状态',
      component: <Checkbox.Group options={certificateStatusOptions} />,
      span: 4,
    },
    {
      name: 'personnelType',
      label: '人员分类',
      component: <Checkbox.Group options={personnelTypeOptions} />,
      span: 4,
    },
  ];

  // 描述信息组件
  const descriptionComponent = (
    <StatisticsDescription
      total={statistics.total}
      valid={statistics.valid}
      invalid={statistics.invalid}
    />
  );

  return (
    <div className="personnel">
      <ListRequest
        apiUrl={`${SPPREFIX}/safetyProduct/oneCompanyOneFile/list`}
        columns={newColumns}
        filterItems={extendedFilterItems}
        updateTrigger={updateTrigger}
        showSizeChanger={true}
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
        serviceResHandle={serviceResHandle}
        inlineButtons
        tableBordered={true}
        descriptionComponent={descriptionComponent}
      />
      <PersonnelDetailModal
        visible={modalVisible}
        onClose={handleModalClose}
        data={selectedRecord}
      />
    </div>
  );
};

export default Personnel;
