import React, { useState, useEffect } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton, CustomButton, DynamicFormItem } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { Dayjs } from 'dayjs';
import {
  deleteTicket,
  getTicketAttachAddr,
  getTicketDetail,
  saveTicket,
} from '@/services/companyInfo/ticketMgr';
import { message, Spin } from 'antd';
import styles from './index.less';
import { revertTicketTime, setTicketTime, ticketFormCheckProcess } from './utils';
import FilePreview from '@/components/FilePreview';

let fileUrl = '';
let attachName = '';
let savePath = '';

interface TicketMgrProps {
  tableColumns: any[];
  HomeTitle: string;
  filterItems: any[];
  customButtons: CustomButton[];
  formItems: DynamicFormItem[];
  configMap?: {
    [key: string]: () => Promise<{ label: string; value: any }[]>;
  };
  handleDataBeforeSave?: (data: any) => any;
  handleDataBeforeInit?: (data: any) => any;
  handleSearch?: (data: any) => any;
  dateNames?: string[];
  imgNames?: string[];
  keyName: string; // 用于匹配 workType
  x?: number;
}

let workType = '';

const TicketMgr: React.FC<TicketMgrProps> = ({
  tableColumns,
  HomeTitle,
  filterItems,
  customButtons,
  configMap = {},
  formItems,
  handleDataBeforeSave = data => data,
  handleDataBeforeInit = data => data,
  handleSearch = data => data,
  keyName = '',
  imgNames,
  x,
}) => {
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [columns, setColumns] = useState([...tableColumns]);
  const [workTypeLoaded, setWorkTypeLoaded] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    deleteHandler,
    selectedRowsDel,
  } = useCommonFormPage({ HomeTitle });

  useEffect(() => {
    const fetchWorkType = async () => {
      if (keyName) {
        const dict = (await dictConfigWrapper('work_type')) || [];
        const matchedOption = dict.find((v: any) => v.label && v.label.includes(keyName));
        workType = matchedOption ? matchedOption.value : undefined;
      }
      setWorkTypeLoaded(true); // 加载完成
    };

    fetchWorkType();
  }, []);

  const { optionsMap } = useGeneralizedConfig(setTableFilters, setColumns, {
    ...configMap,
    workStatus: () => dictConfigWrapper('work_status'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = async () => {
    selectedRowsDel(deleteTicket);
  };

  const getStringTime = (time: Dayjs, format = 'YYYY-MM-DD') => {
    return time?.format?.(format);
  };

  const onSave = async (formData: any) => {
    const values = await saveHandler(formData, saveTicket, (data: any) => {
      let newData = setTicketTime(data);
      newData = handleDataBeforeSave(newData);
      newData = ticketFormCheckProcess(newData);
      if (typeof newData === 'boolean' && !newData) return false;
      newData.workType = workType;
      return newData;
    });
    return values;
  };

  const handleApplyTime = (params: any) => {
    const { applyTime, ...rest } = handleSearch(params);
    let startTime;
    let endTime;
    if (Array.isArray(applyTime) && applyTime.length === 2) {
      startTime = getStringTime(applyTime[0]);
      endTime = getStringTime(applyTime[1]);
    }
    return {
      ...rest,
      startTime,
      endTime,
      workType,
    };
  };

  const commonDetailHandle = (data: any) => handleDataBeforeInit(revertTicketTime(data));

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getTicketDetail, commonDetailHandle);
  };

  const handleDeleteOne = (record: any) => {
    deleteHandler([record.id], deleteTicket);
  };

  const handleTicketPreview = async (record: any) => {
    try {
      const {
        code,
        data: { data: { downloadUrl, atchName, savePath: path } } = { data: {} },
        msg,
      } = await getTicketAttachAddr(record.id);
      if (code === '200') {
        fileUrl = `${downloadUrl}`;
        attachName = atchName;
        savePath = path;
        setPreviewOpen(true);
      } else message.error(msg);
    } catch {
      message.error('获取票据信息失败');
    }
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      fixed: x ? 'right' : undefined,
      render: (_text: any, record: any) => {
        const { workStatus } = record;
        const showEditValue = (optionsMap.workStatus || []).find(v => v.value === '待签发')?.value;
        const showEdit = workStatus === showEditValue;
        return (
          <div>
            {!showEdit && (
              <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            )}
            <CommonLinkButton onClick={() => handleDeleteOne(record)}>删除</CommonLinkButton>
            <CommonLinkButton onClick={() => handleTicketPreview(record)}>票据</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  const getExitParams = () => {
    const params: Record<string, any> = {};
    if (x) params.scroll = { x };
    return params;
  };

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={onSave}
      formComp={<BaseInfoForm formItems={formItems} openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        imageFieldNames: imgNames,
        imgStringFormat: true,
      }}
      formLoading={formLoading}
      gutter={[6, 0]}
    >
      {!workTypeLoaded ? (
        <div className={styles.tableSpin}>
          <Spin spinning />
        </div>
      ) : (
        <ListRequest
          apiUrl={`${SPPREFIX}/basicinformation/special/ticketList`}
          filterItems={[...tableFilters]}
          updateTrigger={updateTrigger}
          searchParamsHandle={handleApplyTime}
          columns={newColumns}
          rowSelection
          onRowSelect={onRowSelect}
          customButtons={customButtons}
          inlineButtons
          buttonCol={1}
          labelCol={{ span: 8 }}
          scrollYDelta={48}
          {...getExitParams()}
        />
      )}
      <FilePreview
        width={1000}
        showDownloadBtn
        path={fileUrl}
        open={previewOpen}
        attachName={attachName}
        savePath={savePath}
        onClose={() => setPreviewOpen(false)}
      />
    </CommonPage>
  );
};

export default TicketMgr;
