import React from 'react';
import { Dropdown, Button } from 'antd';

interface TemplateItem {
  label: string;
  url: string;
}

interface DownloadDropdownProps {
  buttonTitle: string;
  templates: TemplateItem[];
  icon?: React.ReactNode;
  style?: React.CSSProperties; // 新增 style 属性
}

const DownloadDropdown: React.FC<DownloadDropdownProps> = ({
  buttonTitle,
  templates,
  icon,
  style, // 解构 style
}) => {
  const items = templates.map(item => ({
    key: item.url,
    label: (
      <Button
        type="text"
        onClick={() => window.open(item.url, '_blank')}
        icon={icon}
        style={{ padding: 0 }}
      >
        {item.label}
      </Button>
    ),
  }));

  return (
    <Dropdown menu={{ items }} trigger={['click']}>
      <Button icon={icon} style={style}>
        {buttonTitle}
      </Button>
    </Dropdown>
  );
};

export default DownloadDropdown;
