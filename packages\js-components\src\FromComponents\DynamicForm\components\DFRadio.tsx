import { Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import { DynamicFormItem } from '../interface';

interface DFRadioProps {
  item: DynamicFormItem;
  value?: any; // 用于受控组件
  formStoreHandle?: (item: DynamicFormItem, option: any) => void;
  onChange?: (value: any) => void; // 用于受控组件
}

const DFRadio: React.FC<DFRadioProps> = ({ item, value, onChange, formStoreHandle }) => {
  const { onOptionsLoaded } = item.radioInfo || {};
  const [options, setOptions] = useState(item.options || []);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchOptions = async () => {
      let initOptions: any[] = [];
      if (typeof item.optionsServices === 'function') {
        setLoading(true);
        try {
          const res = await item.optionsServices();
          initOptions = res || [];
          setOptions(initOptions);
        } catch (error) {
          setOptions([]);
          // 这里可以根据需要添加错误提示
        } finally {
          setLoading(false);
        }
      } else {
        initOptions = item.options || [];
        setOptions(item.options || []);
      }
      if (onOptionsLoaded) onChange?.(onOptionsLoaded(initOptions));
    };
    fetchOptions();
  }, [item.optionsServices, item.options]);

  const radioChangeHandle = (e: any) => {
    const selectedOption = options.find(opt => opt.value === e.target.value);
    formStoreHandle?.(item, selectedOption);
    onChange?.(e.target.value);
  };

  return (
    <Radio.Group options={options} value={value} onChange={radioChangeHandle} disabled={loading} />
  );
};

export default DFRadio;
