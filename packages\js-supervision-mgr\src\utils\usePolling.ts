import { useEffect, useState } from 'react';
import { request } from './net';

export interface Notification {
  content: string;
  address?: string;
}

const usePolling = (url: string, params: Record<string, any>, interval: number) => {
  const [data, setData] = useState<Notification[]>([]);
  useEffect(() => {
    let isMounted = true;
    const fetchData = async () => {
      const response = await request.get(url, params);
      if (response?.data?.length === 0) return;
      if (isMounted) setData(response.data);
    };
    fetchData();
    const intervalId = setInterval(fetchData, interval);
    return () => {
      isMounted = false;
      clearInterval(intervalId);
    };
    // 依赖 url、interval 以及 params
  }, [url, interval, JSON.stringify(params)]);
  return data;
};

export default usePolling;
