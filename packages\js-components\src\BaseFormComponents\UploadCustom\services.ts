import { PRIVIEW_SERVER } from '../../global';

export async function batchDeletFile(params?: any, options?: RequestInit) {
  // 基础 URL
  let url = `${PRIVIEW_SERVER}/api/batchDelet`;
  // 添加参数
  if (params) {
    const queryParts = [];
    if (params.bucket) {
      queryParts.push(`bucket=${encodeURIComponent(params.bucket)}`);
    }
    if (params.objectNames && Array.isArray(params.objectNames)) {
      params.objectNames.forEach((objectName: any) => {
        queryParts.push(`objectNames=${encodeURIComponent(objectName)}`);
      });
    }
    // 拼接查询字符串
    url += `?${queryParts.join('&')}`;
  }
  // 使用 fetch 发送 GET 请求
  const response = await fetch(url, {
    method: 'GET',
    ...(options || {}),
  });
  // 解析 JSON 响应
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json();
}
