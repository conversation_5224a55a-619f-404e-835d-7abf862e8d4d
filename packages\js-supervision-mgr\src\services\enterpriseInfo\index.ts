import { request } from '@/utils/net';

export async function getIndustriesList() {
  return request.get(
    `${SPPREFIX}/basicinformation/changeIndustries/industriesList?pageNo=1&pageSize=10000`,
  );
}

export async function getCompanyList() {
  return request.get(
    `${SPPREFIX}/basicinformation/enterpriseInformation/infoList?pageNo=1&pageSize=10000`,
  );
}

export async function saveEnterpriseInfo(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/enterpriseInformation/saveInfo`, params);
}

export async function getEnterpriseInfoDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/enterpriseInformation/infoDetail`, {
    id,
  });
}

export async function deleteEnterpriseInfo(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/enterpriseInformation/removeInfo`, params);
}
