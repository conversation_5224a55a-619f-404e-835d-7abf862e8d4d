import UploadCustom from '@/components/UploadCustom';
import { saveMaterial } from '@/services/systemMaintenance/material';
import { Form, Modal, Select, message } from 'antd';
import React, { useRef } from 'react';

const typeEnum: { [key: string]: string } = {
  1: '上传图片',
  2: '上传视频',
  3: '上传音频',
};

const fileTypeEnum: { [key: string]: string } = {
  1: 'image',
  2: 'video',
  3: 'audio',
};

const UploadModal: React.FC<{
  groupList?: any[];
  type: string;
  neddGroup?: boolean;
  groupId?: string;
  open?: boolean;
  onClose?: (type?: string) => void;
}> = ({ groupList, type, neddGroup = true, groupId, open, onClose }) => {
  const [form] = Form.useForm();
  const uploadRef = useRef<any>();

  const onOk = () => {
    form.validateFields().then(async (values) => {
      const params = values.fileList?.map((item: any) => ({
        materialName: item.name,
        materialGroupId: groupId || values.materialGroupId,
        materialTypeId: type,
        attachment: {
          ...(item?.response?.data || {}),
        },
      }));
      const res = await saveMaterial({ materialData: params });
      if (res.code === '200') {
        message.success('上传成功');
        onClose?.('search');
        uploadRef?.current?.remove();
      } else {
        message.error(res?.msg || '上传失败');
      }
    });
  };

  return (
    <Modal
      title={typeEnum[type]}
      open={open}
      onCancel={() => {
        onClose?.();
        uploadRef?.current?.remove(true);
      }}
      onOk={onOk}
      width={680}
    >
      <Form form={form}>
        {type === '1' && neddGroup && (
          <Form.Item label="分组" name="materialGroupId" rules={[{ required: true, message: '请选择分组' }]}>
            <Select
              fieldNames={{
                label: 'materialGroupName',
                value: 'materialGroupId',
              }}
              options={groupList}
              placeholder="请选择分组"
            />
          </Form.Item>
        )}
        <Form.Item name="fileList" rules={[{ required: true, message: '请上传文件' }]}>
          <UploadCustom ref={uploadRef} dragable type={fileTypeEnum[type]} listType="text" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UploadModal;
