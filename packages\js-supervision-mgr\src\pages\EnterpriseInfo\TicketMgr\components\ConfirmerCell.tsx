import React from 'react';
import { Space, Popconfirm, Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { DFSignaturePad } from 'jishan-components';

interface Props {
  signature: string;
  isDefault: boolean;
  onSignatureChange: (value: string) => void;
  onDelete: () => void;
}

const ConfirmerCell: React.FC<Props> = ({ signature, isDefault, onSignatureChange, onDelete }) => (
  <Space>
    <DFSignaturePad type="primary" value={signature} onChange={onSignatureChange} />
    {!isDefault && (
      <Popconfirm title="确定删除该行吗？" onConfirm={onDelete} okText="是" cancelText="否">
        <Button type="link" danger icon={<DeleteOutlined />} />
      </Popconfirm>
    )}
  </Space>
);

export default ConfirmerCell;
