import React, { useState, useEffect } from 'react';
import { FormContainer } from 'jishan-components';
import { message } from 'antd';
import { fetchSelectOptions } from '@/utils/commonFunction';
import {
  getLevelFieldsOptions,
  getRiskPointDeatil,
  saveRiskPoint,
} from '@/services/riskCharge/riskPoint';
import LevelFormItems from './LevelFormItems';
import {
  formItems,
  analysisItems,
} from '@/pages/RiskLevelMgr/RiskLevelList/components/BaseInfoForm';
import styles from '../index.less';

const newFormItems = [...formItems];
newFormItems[0] = { quickItemParams: ['风险点名称', 'riskName', 'Input', false], span: 12 };
newFormItems[1].optionsServices = undefined;
newFormItems[2].optionsServices = undefined;
newFormItems[3].optionsServices = undefined;

const getOptions = async (fieldName: string) => {
  const res = await getLevelFieldsOptions({ fieldName });
  return fetchSelectOptions(() => Promise.resolve(res), 'description', 'matchValue');
};

export const LevelContainer: React.FC<{ record: any; onClose: any }> = ({ record, onClose }) => {
  const [initValue, setInitValue] = useState<Record<string, any>>({});
  const [formLoading, setFormLoading] = useState(false);
  const [levelInfo, setLevelInfo] = useState<Record<string, any>[]>([]);
  const [selectOptions, setSelectOptions] = useState<{
    accidentProbability: any[];
    accidentFrequency: any[];
    accidentConsequence: any[];
  }>({
    accidentProbability: [],
    accidentFrequency: [],
    accidentConsequence: [],
  });

  const detailHandle = async () => {
    setFormLoading(true);
    try {
      const detailInfo = await getRiskPointDeatil(record.id);
      const { code, data } = detailInfo;
      if (code === '200') {
        setInitValue(data);
        setLevelInfo(data.attBatchParams || []);
      }
    } catch (error) {
      message.error('详情获取或解析失败，请稍后再试！');
    }
    setFormLoading(false);
  };

  const fetchAllOptions = async () => {
    try {
      const [accidentProbability, accidentFrequency, accidentConsequence] = await Promise.all([
        getOptions('accidentProbability'),
        getOptions('accidentFrequency'),
        getOptions('accidentConsequence'),
      ]);
      setSelectOptions({
        accidentProbability,
        accidentFrequency,
        accidentConsequence,
      });
    } catch (e) {
      message.error('下拉选项获取失败');
    }
  };

  const saveHandler = async (formData: any) => {
    const newFormData = { ...formData };
    newFormData.id = record.id;
    console.log('newFormData: ', newFormData);

    const res = await saveRiskPoint(newFormData);
    if (res.code === '200') {
      message.success('保存成功！');
      onClose();
      return true;
    }
    message.error(res.msg);
    return false;
  };

  useEffect(() => {
    if (record.id) detailHandle();
    if (!newFormItems[1].options?.length) fetchAllOptions();
  }, [record]);

  if (!newFormItems[1].options?.length) {
    newFormItems[1].options = selectOptions.accidentProbability;
    newFormItems[2].options = selectOptions.accidentFrequency;
    newFormItems[3].options = selectOptions.accidentConsequence;
  }

  return (
    <FormContainer
      visible
      onClose={onClose}
      onSubmit={saveHandler}
      formFieldOptions={{
        imageFieldFormatControl: false,
        imageFieldNames: ['afterPhotos'],
        batchImgName: 'riskBatchParams',
      }}
      formLoading={formLoading}
      containerClass={styles.levelContainer}
      formClass={styles.levelForm}
      openType="edit"
      initialValues={initValue}
      formComp={
        <LevelFormItems analysisItems={analysisItems} levelInfo={levelInfo} items={newFormItems} />
      }
    />
  );
};
