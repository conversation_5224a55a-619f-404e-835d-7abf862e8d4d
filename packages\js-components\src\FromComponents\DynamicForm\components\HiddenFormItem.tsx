import React, { useEffect } from 'react';
import { Form } from 'antd';
import { DynamicFormItem } from '../interface';
import { getRelatedValue } from '../../../utils/commonFunction';

interface HiddenFormItemProps {
  item: DynamicFormItem;
  getName: (baseName: string) => string;
  formStore: Record<string, any>;
  storeUpdate: boolean;
  form: any; // 你的 form 类型
}

const HiddenFormItem: React.FC<HiddenFormItemProps> = ({
  item,
  getName,
  formStore,
  storeUpdate,
  form,
}) => {
  useEffect(() => {
    // 监听 storeUpdate，每次变化时同步值到表单
    if (form && item.relatedValue) {
      const value = getRelatedValue(formStore, getName, item.relatedValue);
      value && form.setFieldsValue({ [getName(item?.name || '')]: value });
    }
  }, [storeUpdate, item]);

  return <Form.Item name={getName(item?.name || '')} style={{ display: 'none' }} />;
};

export default HiddenFormItem;
