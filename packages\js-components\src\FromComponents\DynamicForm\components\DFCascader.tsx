import React, { useEffect, useState } from 'react';
import { CascaderOption, DynamicFormItem } from '../interface';
import { Cascader } from 'antd';

export interface DFCascaderProps {
  placeholder?: string;
  item: DynamicFormItem; // 用于获取 item 的其他属性
  value?: any; // 用于表单受控
  onChange?: (value: any, selectedOptions?: CascaderOption[]) => void; // 用于表单受控
}

const DFCascader: React.FC<DFCascaderProps> = ({ placeholder, item, value, onChange }) => {
  const { cascaderOptions: options, cascaderOptionsServices } = item;
  const [cascaderOptions, setCascaderOptions] = useState<CascaderOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (cascaderOptionsServices) {
        setLoading(true);
        try {
          const data = await cascaderOptionsServices();
          setCascaderOptions(data || []);
        } catch (error) {
          setCascaderOptions([]);
        } finally {
          setLoading(false);
        }
      } else if (Array.isArray(options)) {
        setCascaderOptions(options);
      }
    };
    fetchData();
  }, [options, cascaderOptionsServices]);

  return (
    <Cascader
      placeholder={placeholder}
      options={cascaderOptions}
      style={{ width: '100%' }}
      loading={loading}
      value={value}
      onChange={onChange}
    />
  );
};

export default DFCascader;
