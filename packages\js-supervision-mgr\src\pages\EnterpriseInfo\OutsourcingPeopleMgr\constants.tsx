import React from 'react';
import { CustomButton, DownloadDropdown, SearchInput, JSCUtils } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';
import { tableColumns as workerColumns } from '../CommonPersonPage/constants';

const { getTemplateDownloadUrl } = JSCUtils;

const newColumns = workerColumns.map(col => ({ ...col }));
// 在企业名称后面插入外包公司单位（企业名称下标为1）
newColumns.splice(2, 0, { title: '外包公司单位', dataIndex: 'outsourcingCompany', width: 200 });
// 修改人员姓名为作业人员姓名（原下标为2，插入后变为3）
newColumns[3].title = '作业人员姓名';

export const HomeTitle = '外包公司人员管理';

export const filterItems = [
  {
    name: 'outsourcingCompany',
    label: '外包公司单位名称',
    component: <SearchInput placeholder="请输入外包公司单位名称" />,
  },
  {
    name: 'personName',
    label: '作业人员姓名',
    component: <SearchInput placeholder="请输入作业人员姓名" />,
  },
];

const notSpecialUrl: [string, string] = [
  '非特殊作业人员模板.xlsx',
  '20250717/cf718c84-2b00-4f2d-990f-4625c8d50fbc.xlsx',
];
const specialUrl: [string, string] = [
  '特殊作业人员模板.xlsx',
  '20250717/5cd2b9b5-2057-466e-a26c-928138d0bfd6.xlsx',
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '导入', ...baseButtonStyle },
  {
    text: '模板下载',
    ...baseButtonStyle,
    component: (
      <DownloadDropdown
        style={baseButtonStyle}
        buttonTitle="模板下载"
        templates={[
          { label: '非特殊作业人员模板', url: getTemplateDownloadUrl(...notSpecialUrl) },
          { label: '特殊作业人员模板', url: getTemplateDownloadUrl(...specialUrl) },
        ]}
      />
    ),
  },
];

export const tableColumns: any[] = newColumns;
