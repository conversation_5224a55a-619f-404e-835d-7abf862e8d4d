import { request } from '@/utils/net';

export async function saveMeasure(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/riskLevel/control/saveMeasure`, params);
}

export async function getMeasureDetail(id: number) {
  return request.get(`${SPPREFIX}/riskLevel/control/measureDetail`, {
    id,
  });
}

export async function deleteMeasure(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/riskLevel/control/removeMeasure`, params);
}
