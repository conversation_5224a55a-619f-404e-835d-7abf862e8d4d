// BarChart.tsx
import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface BarChartProps {
  barData: {
    levelCount: string;
    level: string;
    proportion: string;
  }[];
}

const colorList = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE'];

const BarChart: React.FC<BarChartProps> = ({ barData }) => {
  const barChartRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (barChartRef.current && Array.isArray(barData) && barData.length > 0) {
      const barChart = echarts.init(barChartRef.current);
      // 将 barData 拆分为两个数组
      const levels = barData.map(item => `${item.level}预警`);
      const counts = barData.map(item => Number(item.levelCount));
      const barOption = {
        title: {
          text: '预警分级统计',
          left: 'center',
        },
        grid: {
          top: 90,
        },
        xAxis: {
          type: 'category',
          data: levels,
          axisLabel: {
            interval: 0, // 强制显示所有标签
          },
        },
        yAxis: {
          type: 'value',
          minInterval: 1, // 确保刻度间隔至少为1
          axisLabel: {
            formatter: '{value}', // 保持默认格式
          },
        },
        legend: {
          data: levels, // 直接使用 levels 作为图例数据
          top: '30', // 图例位置在顶部
          icon: 'rect', // 图例图标为矩形色块
        },
        series: levels.map((level, index) => ({
          name: level,
          type: 'bar',
          stack: 'total', // 所有系列共享同一个堆叠组
          data: levels.map((_, i) => (i === index ? counts[index] : 0)),
          itemStyle: {
            color: colorList[index % colorList.length],
          },
          barWidth: '30%', // 增大宽度以填充空间
          // 隐藏数据为 0 的柱子
          emphasis: { disabled: true }, // 取消悬停效果
        })),
      };
      barChart.setOption(barOption);
    }
  }, [barData]);
  return <div ref={barChartRef} style={{ width: '100%', height: '100%' }} />;
};
export default BarChart;
