import React from 'react';
import PageHeader from '../PageHeader';
import <PERSON>Container, { FormContainerProps } from '../../FromComponents/FormContainer';

export interface CommonPageProps extends Omit<FormContainerProps, 'formComp' | 'visible'> {
  title: string;
  backIconClick: () => void;
  formComp?: React.ReactNode;
  formVisible: boolean;
  children: React.ReactNode; // 用于插槽传递 ListRequest
  backIcon?: string;
}

const CommonPage: React.FC<CommonPageProps> = ({
  title,
  backIconClick,
  formComp,
  formVisible,
  children,
  backIcon,
  ...modalFormProps
}) => {
  return (
    <div className="common-page">
      <PageHeader
        backIcon={backIcon}
        showBackIcon={formVisible}
        title={title}
        onClick={backIconClick}
      />
      {!formVisible && <div className="common-page-body">{children}</div>}
      <FormContainer {...modalFormProps} visible={formVisible} formComp={formComp || null} />
    </div>
  );
};

export default CommonPage;
