import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '管控措施';

export const riskModalSelectParams = {
  apiUrl: `${SPPREFIX}/risk/point/riskPointList`,
  filterItems: [],
  hideActionButtons: true,
  width: 700,
  rowSelectType: 'radio' as any,
  valueName: 'riskName',
  columns: [
    { title: '风险点名称', dataIndex: 'riskName', ellipsis: true, width: 250 },
    { title: '风险点编码', dataIndex: 'riskCode', width: 200 },
  ],
  relatedSearchParams: ['enterpriseName'],
};

export const filterItems = [
  {
    name: 'controlMeasures',
    label: '管控措施',
    component: <SearchInput placeholder="请输入管控措施" />,
  },
  {
    name: 'riskLevel',
    label: '负责人',
    component: <SearchInput placeholder="请输入负责人" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName' },
  { title: '风险点名称', dataIndex: 'riskName' },
  { title: '风险分析', dataIndex: 'riskAnalysis' },
  { title: '危害后果', dataIndex: 'hazardConsequence' },
  { title: '管控措施', dataIndex: 'controlMeasures' },
  { title: '风险等级', dataIndex: 'riskLevel' },
  { title: '风险管控层级', dataIndex: 'controlLevel' },
  { title: '上次评估日期', dataIndex: 'lastEvaluationDate' },
  { title: '落实情况', dataIndex: 'implementationStatus' },
  { title: '相关资料', dataIndex: 'relatedMaterials' },
  { title: '责任单位', dataIndex: 'responsibleUnit' },
  { title: '责任人', dataIndex: 'responsiblePerson' },
  { title: '责任人电话', dataIndex: 'responsiblePhone' },
  { title: '审核人', dataIndex: 'reviewer' },
  { title: '审核日期', dataIndex: 'reviewDate' },
  { title: '复评日期', dataIndex: 'reevaluationDate' },
];
