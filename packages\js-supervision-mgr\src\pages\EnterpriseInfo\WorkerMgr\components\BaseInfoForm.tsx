import React from 'react';
import { Radio, Form, Col, FormInstance } from 'antd';
import { DynamicForm, SectionTitle, FormOpenType } from 'jishan-components';
import { defaultRadioOptions } from '@/utils/constants';
import { formItems } from '../../CommonPersonPage/constants';

interface BaseInfoFormProps {
  form?: FormInstance<any>;
  openType: FormOpenType;
  isSpecialWorker: string;
  onSpecialWorkerChange: (e: any) => void;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({
  form,
  openType,
  isSpecialWorker,
  onSpecialWorkerChange,
}) => {
  return (
    <>
      <SectionTitle title="人员基础信息" />
      <DynamicForm items={formItems} openType={openType} form={form} />
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="特殊作业人员"
          name="isSpecialWorker"
          initialValue="0"
        >
          <Radio.Group
            value={isSpecialWorker}
            options={defaultRadioOptions}
            onChange={onSpecialWorkerChange}
          />
        </Form.Item>
      </Col>
    </>
  );
};

export default BaseInfoForm;
