import React, { useState, useEffect } from 'react';
import MeasureTable from './MeasureTable';
import { FormInstance } from 'antd';
import { defaultRadioOptions } from '@/utils/constants';

const defaultMeasure = {
  measure: '',
  involved: defaultRadioOptions.find(v => v.label === '否')?.value || '',
  confirmer: '',
  signature: '',
};

const initialData = [
  {
    key: 1,
    ...defaultMeasure,
    isDefault: true,
  },
];

interface SafetyProps {
  form?: FormInstance<any> | undefined;
  formInitValue?: any;
}

const SafetyMeasuresList: React.FC<SafetyProps> = ({ form, formInitValue }) => {
  const [dataSource, setDataSource] = useState(initialData);
  const [headerSignature, setHeaderSignature] = useState<string>('');
  const [draft, setDraft] = useState('');

  useEffect(() => {
    const { attBatchParams, blindDrawingName } = formInitValue || {};
    if (attBatchParams) {
      setDataSource(
        attBatchParams.map((item: any, key: number) => ({
          measure: item.measureContent,
          involved: item.isInvolveRisk,
          signature: item.confirmer, // 这里 signature 字段即为签名人
          key,
        })) || [],
      );
    }
    if (blindDrawingName) setDraft(blindDrawingName);
  }, [formInitValue]);

  // 工具函数：同步 dataSource 到表单字段 attBatchParams
  const syncToForm = (data: typeof dataSource) => {
    // 只同步非默认行
    const attBatchParams = data.map(item => ({
      measureContent: item.measure,
      isInvolveRisk: item.involved,
      confirmer: item.signature, // 这里 signature 字段即为签名人
    }));
    form?.setFieldValue?.('attBatchParams', attBatchParams);
  };

  // 新增
  const handleAdd = () => {
    const newKey = dataSource.length ? Math.max(...dataSource.map(d => Number(d.key) || 0)) + 1 : 1;
    const newData = [
      ...dataSource,
      {
        ...defaultMeasure,
        key: newKey,
        isDefault: false,
      },
    ];
    setDataSource(newData);
    syncToForm(newData);
  };

  // 删除
  const handleDelete = (key: number) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    syncToForm(newData);
  };

  // 是否涉及风险变化
  const handleInvolvedChange = (key: number, value: string) => {
    const newData = dataSource.map(item =>
      item.key === key ? { ...item, involved: value } : item,
    );
    setDataSource(newData);
    syncToForm(newData);
  };

  // 安全措施内容变化
  const handleMeasureChange = (key: number, value: string) => {
    const newData = dataSource.map(item => (item.key === key ? { ...item, measure: value } : item));
    setDataSource(newData);
    syncToForm(newData);
  };

  const handleDrafterChange = (value: string) => {
    setDraft(value);
    form?.setFieldValue?.('blindDrawingName', value);
  };

  // 行签名变化
  const handleRowSignature = (key: number, value: string) => {
    const newData = dataSource.map(item =>
      item.key === key ? { ...item, signature: value, confirmer: value ? '签名人' : '' } : item,
    );
    setDataSource(newData);
    syncToForm(newData);
  };

  // 表头签名变化
  const handleHeaderSignature = (value: string) => {
    setHeaderSignature(value);
    if (value) {
      const newData = dataSource.map(item => ({
        ...item,
        signature: value,
        confirmer: '签名人',
      }));
      setDataSource(newData);
      syncToForm(newData);
    }
  };

  return (
    <MeasureTable
      handleAdd={handleAdd}
      dataSource={dataSource}
      headerSignature={headerSignature}
      onHeaderSignature={handleHeaderSignature}
      onMeasureChange={handleMeasureChange}
      onDrafterChange={handleDrafterChange}
      onInvolvedChange={handleInvolvedChange}
      onRowSignature={handleRowSignature}
      onDelete={handleDelete}
      draft={draft}
    />
  );
};

export default SafetyMeasuresList;
