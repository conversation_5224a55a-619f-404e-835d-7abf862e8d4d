import React, { useEffect, useState } from 'react';
import {
  DynamicForm,
  DynamicFormItem,
  FormOpenType,
  ListRequest,
  SectionTitle,
} from 'jishan-components';
import { Button, FormInstance, message, Table, Form } from 'antd';
import imagesImg from '@/assets/images/common/image.png';
import { isProductInRange } from '@/utils/commonFunction';
import styles from '../index.less';
import { levelColumns, levelWarnTip, optionsWrapper } from '../constants';
import { getScoreList } from '@/services/riskLevelMgr/riskLevelList';
import { companyModalSelectParams } from '@/utils/constants';
import { filterItems, tableColumns } from '@/pages/RiskCharge/RiskPoint/constants';

export const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', false],
    span: 12,
    modalSelectInfo: companyModalSelectParams,
  },
  {
    quickItemParams: ['事故发生的可能性', 'accidentProbability', 'Select', false],
    optionsServices: optionsWrapper('accidentProbability'),
    span: 12,
  },
  {
    quickItemParams: ['频繁程度', 'accidentFrequency', 'Select', false],
    optionsServices: optionsWrapper('accidentFrequency'),
    span: 12,
  },
  {
    quickItemParams: [
      '事故产生的后果(满足其中之一则选择该项)',
      'accidentConsequence',
      'Select',
      false,
    ],
    optionsServices: optionsWrapper('accidentConsequence'),
    span: 12,
    labelCol: { span: 12 },
  },
  {
    quickItemParams: ['风险分布图', 'afterPhotos', 'ImgUpload', false],
    imgInfo: { imagesImg, maxNum: 8 },
    span: 12,
  },
  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'dValue' },
  { type: 'hideItem', name: 'enterpriseCode', relatedValue: 'enterpriseName.enterpriseCode' },
];

export const analysisItems: DynamicFormItem[] = [
  {
    quickItemParams: ['风险评估值', 'dValue', 'Input', true, levelWarnTip],
    span: 12,
    disabled: true,
  },
  {
    quickItemParams: ['风险等级', 'riskLevel', 'Input', true, levelWarnTip],
    span: 12,
    disabled: true,
  },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  const [scoreList, setScoreList] = useState<any[]>([]);

  useEffect(() => {
    if (!scoreList.length) {
      try {
        getScoreList().then(res => {
          const { data, msg, code } = res;
          if (code === '200' && data) {
            setScoreList(data.data);
          } else message.error(msg);
        });
      } catch {
        message.error('获取风险定级标准失败');
      }
    }
  }, []);

  // 点击按钮时校验并计算
  const analysis = () => {
    const values = form?.getFieldsValue?.() || {};
    const { accidentConsequence, accidentFrequency, accidentProbability } = values;
    if (!accidentConsequence) {
      message.warning('请选择事故后果');
      return;
    }
    if (!accidentFrequency) {
      message.warning('请选择频繁程度');
      return;
    }
    if (!accidentProbability) {
      message.warning('请选择事故发生可能性');
      return;
    }
    const product = accidentConsequence * accidentFrequency * accidentProbability;
    form?.setFieldValue('dValue', product);
    // 计算 riskLevel
    let riskLevel = '';
    for (const item of scoreList) {
      if (isProductInRange(product, item.dValue)) {
        riskLevel = item.description;
        break;
      }
    }
    form?.setFieldValue('riskLevel', riskLevel);
  };

  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
      <div className={styles.buttonContainer}>
        <Button type="primary" onClick={analysis}>
          开始分析
        </Button>
      </div>
      <SectionTitle title="分析结果" />
      <DynamicForm items={analysisItems} openType={openType} form={form} />
      {openType !== 'view' ? (
        <>
          <SectionTitle title="风险定级" />
          <Table
            columns={levelColumns as any}
            dataSource={scoreList || []}
            rowKey="id"
            pagination={false}
            style={{ width: '100%' }}
          />
        </>
      ) : (
        <div style={{ width: '100%' }}>
          <SectionTitle title="风险点列表" />
          <Form disabled={false}>
            <ListRequest
              apiUrl={`${SPPREFIX}/risk/point/riskPointList`}
              filterItems={[...filterItems]}
              columns={tableColumns}
              inlineButtons
              scrollYDelta={48}
            />
          </Form>
        </div>
      )}
    </>
  );
};

export default BaseInfoForm;
