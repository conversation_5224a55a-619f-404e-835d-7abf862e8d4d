import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '岗位维护';

export const filterItems = [
  {
    // TODO 根据登录用户回填
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入岗位名称" />,
  },
  {
    name: 'positionName',
    label: '岗位名称',
    component: <SearchInput placeholder="请输入岗位名称" />,
  },
  {
    name: 'positionCategory',
    label: '岗位类别',
    component: <SearchInput placeholder="请输入岗位类别" />,
    span: 5,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName' },
  { title: '岗位名称', dataIndex: 'positionName' },
  { title: '岗位类别', dataIndex: 'positionCategory' },
  { title: '岗位等级', dataIndex: 'positionLevel' },
  { title: '岗位状态', dataIndex: 'positionStatus' },
  { title: '岗位职责', dataIndex: 'responsibilities' },
  { title: '所属部门', dataIndex: 'department' },
  { title: '工作地点', dataIndex: 'workLocation' },
];
