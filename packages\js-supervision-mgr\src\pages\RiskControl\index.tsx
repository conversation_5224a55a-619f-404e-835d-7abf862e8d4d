import React, { useEffect, useState } from 'react';
import { Row, Col, message } from 'antd';
import Pie<PERSON><PERSON> from './components/PieChart';
import Bar<PERSON>hart from './components/BarChart';
import RankingChart from './components/RankingChart';
import TimeSelector, { DateType } from './components/TimeSelector';
import { riskAnalysisList } from '@/services/riskControl';
import './index.less';

const ChartComponent: React.FC = () => {
  const [qryType, setRequestType] = useState<DateType>('season');
  const [typeCount, setTypeCount] = useState<any[]>([]);
  const [barData, setBarData] = useState<any[]>([]);
  const [rankData, setRankData] = useState<any[]>([]);
  const [dateRange, setDateRange] = useState<{ startTime: string; endTime: string }>({
    startTime: '',
    endTime: '',
  });

  const queryDataList = async () => {
    const params = {
      qryType,
      ...dateRange,
    };
    const res = await riskAnalysisList(params);
    if (res.code === '200') {
      const { data: { levelCount, owningRegionCount, typeCount } = {} } = res;
      setBarData(levelCount);
      setTypeCount(typeCount);
      setRankData(owningRegionCount);
    } else message.error(res.msg);
  };

  const handleRequestTypeChange = (type: DateType) => {
    setRequestType(type);
  };

  const onDateRangeChange = (range: { startTime: string; endTime: string } | null) => {
    if (range) {
      setDateRange(range);
    } else setDateRange({ startTime: '', endTime: '' });
  };

  useEffect(() => {
    queryDataList();
    const handleResize = () => {
      // Resize logic if needed
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    queryDataList();
  }, [qryType, dateRange]);

  return (
    <div className="alert-analysis-page common-page ">
      <Row className="spacer-row">
        <TimeSelector
          queryType={qryType}
          onDateRangeChange={onDateRangeChange}
          onRequestTypeChange={handleRequestTypeChange}
        />
      </Row>
      <Row className="charts-row">
        <Col span={10} className="chart-col">
          <PieChart typeCount={typeCount} />
        </Col>
        <Col span={6}>
          <RankingChart rankData={rankData} />
        </Col>
        <Col span={8}>
          <BarChart barData={barData} />
        </Col>
      </Row>
    </div>
  );
};
export default ChartComponent;
