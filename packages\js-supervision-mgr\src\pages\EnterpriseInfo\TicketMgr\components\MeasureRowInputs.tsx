import React from 'react';
import { Input, Space } from 'antd';
import styles from '../index.less';

interface Props {
  measure: string;
  drafter: string;
  isDefault: boolean;
  onMeasureChange: (value: string) => void;
  onDrafterChange: (value: string) => void;
}

const MeasureRowInputs: React.FC<Props> = ({
  measure,
  drafter,
  isDefault,
  onMeasureChange,
  onDrafterChange,
}) => (
  <Space direction="vertical" className={styles.rowInput}>
    <Input
      placeholder="请输入安全措施"
      value={measure}
      onChange={e => onMeasureChange(e.target.value)}
    />
    {isDefault && (
      <div className={styles.drafter}>
        <span>编制人：</span>
        <Input
          placeholder="请输入编制人"
          value={drafter}
          onChange={e => onDrafterChange(e.target.value)}
          style={{ width: 120 }}
        />
      </div>
    )}
  </Space>
);

export default MeasureRowInputs;
