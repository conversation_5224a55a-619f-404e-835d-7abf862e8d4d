import React from 'react';
import { Table, Space, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import MeasureRowInputs from './MeasureRowInputs';
import InvolvedRadio from './InvolvedRadio';
import Confirmer<PERSON>ell from './ConfirmerCell';
import { DFSignaturePad } from 'jishan-components';

interface MeasureItem {
  key: number;
  measure: string;
  involved: string;
  confirmer: string;
  signature: string;
  isDefault: boolean;
}

interface Props {
  dataSource: MeasureItem[];
  headerSignature: string;
  onHeaderSignature: (value: string) => void;
  onMeasureChange: (key: number, value: string) => void;
  onDrafterChange: (value: string) => void;
  onInvolvedChange: (key: number, value: string) => void;
  onRowSignature: (key: number, value: string) => void;
  onDelete: (key: number) => void;
  handleAdd: () => void;
  draft: string;
}

const MeasureTable: React.FC<Props> = ({
  dataSource,
  headerSignature,
  onHeaderSignature,
  onMeasureChange,
  onDrafterChange,
  onInvolvedChange,
  onRowSignature,
  onDelete,
  handleAdd,
  draft,
}) => {
  const columns: ColumnsType<MeasureItem> = [
    {
      title: '序号',
      dataIndex: 'key',
      width: 90,
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '安全措施',
      dataIndex: 'measure',
      render: (_text, record, index) => (
        <MeasureRowInputs
          measure={record.measure}
          drafter={draft}
          isDefault={index === dataSource.length - 1}
          onMeasureChange={value => onMeasureChange(record.key, value)}
          onDrafterChange={value => onDrafterChange(value)}
        />
      ),
    },
    {
      title: '是否涉及',
      dataIndex: 'involved',
      width: 160,
      render: (_text, record) => (
        <InvolvedRadio
          value={record.involved}
          onChange={value => onInvolvedChange(record.key, value)}
        />
      ),
    },
    {
      title: (
        <Space>
          确认人
          <DFSignaturePad
            type="primary"
            showReSign={false}
            showThumbnail={false}
            value={headerSignature}
            onChange={onHeaderSignature}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} />
        </Space>
      ),
      dataIndex: 'confirmer',
      width: 230,
      render: (_text, record) => (
        <ConfirmerCell
          signature={record.signature}
          isDefault={record.isDefault}
          onSignatureChange={value => onRowSignature(record.key, value)}
          onDelete={() => onDelete(record.key)}
        />
      ),
    },
  ];

  return (
    <Table dataSource={dataSource} columns={columns} pagination={false} rowKey="key" bordered />
  );
};

export default MeasureTable;
