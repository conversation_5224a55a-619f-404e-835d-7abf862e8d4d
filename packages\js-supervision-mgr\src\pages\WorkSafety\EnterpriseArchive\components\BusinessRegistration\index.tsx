import React from 'react';
import { Descriptions } from 'antd';

// 工商注册信息数据接口
export interface BusinessRegistrationInfo {
  registrationDate: string;
  registeredCapital: string;
  registrationAddress: string;
  registrationAuthority: string;
  registrationDistrict: string;
  registrationCity: string;
  registrationProvince: string;
  registrationCounty: string;
}

interface BusinessRegistrationProps {
  data?: BusinessRegistrationInfo;
}

// 默认工商注册数据
const defaultBusinessData: BusinessRegistrationInfo = {
  registrationDate: '2000-05-20',
  registeredCapital: '37568.6457',
  registrationAddress: '山西省运城市梅山县梅山县经济技术开发区城西大街东',
  registrationAuthority: '山西省运城市梅山县',
  registrationDistrict: '山西省',
  registrationCity: '运城市',
  registrationProvince: '山西省',
  registrationCounty: '梅山县',
};

const BusinessRegistration: React.FC<BusinessRegistrationProps> = ({
  data = defaultBusinessData,
}) => {
  return (
    <div style={{ padding: '20px 0' }}>
      <Descriptions
        bordered
        size="middle"
        column={{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}
        labelStyle={{
          backgroundColor: '#fafafa',
          fontWeight: 500,
          color: '#666',
          width: '140px',
        }}
        contentStyle={{
          backgroundColor: '#fff',
          color: '#262626',
        }}
      >
        <Descriptions.Item label="注册日期">{data.registrationDate}</Descriptions.Item>
        <Descriptions.Item label="注册资本（万）">{data.registeredCapital}</Descriptions.Item>
        <Descriptions.Item label="注册资本币种">人民币</Descriptions.Item>

        <Descriptions.Item label="注册地址" span={3}>
          {data.registrationAddress}
        </Descriptions.Item>

        <Descriptions.Item label="注册行政区划" span={2}>
          {data.registrationAuthority}
        </Descriptions.Item>
        <Descriptions.Item label="注册地（省）">{data.registrationProvince}</Descriptions.Item>

        <Descriptions.Item label="注册地（市）">{data.registrationCity}</Descriptions.Item>
        <Descriptions.Item label="注册地（区/县）">{data.registrationCounty}</Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default BusinessRegistration;
