// src/components/AlertForm/components/SectionTitle.tsx
import React from 'react';
import { Col } from 'antd';
import { getCompName } from '../../utils/commonFunction';
import './index.less';

interface SectionTitleProps {
  title: string;
}

const SectionTitle: React.FC<SectionTitleProps> = ({ title }) => (
  <Col span={24} className={getCompName('section-title')}>
    <h3>{title}</h3>
    <hr />
  </Col>
);

export default SectionTitle;
