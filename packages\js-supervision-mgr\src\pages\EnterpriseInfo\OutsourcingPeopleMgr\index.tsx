import React from 'react';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import BaseInfoForm from './components/BaseInfoForm';
import CommonPersonPage from '../CommonPersonPage';

const OutsourcingPeopleMgr: React.FC = () => {
  return (
    <CommonPersonPage
      BaseInfoForm={BaseInfoForm}
      homeTitle={HomeTitle}
      customButtons={customButtons}
      filterItems={filterItems}
      tableColumns={tableColumns}
      x={1900}
    />
  );
};

export default OutsourcingPeopleMgr;
