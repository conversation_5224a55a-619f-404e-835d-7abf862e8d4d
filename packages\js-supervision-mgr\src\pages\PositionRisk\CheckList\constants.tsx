import React from 'react';
import { CustomButton, FixedWidthRangePicker, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '岗位排查清单';

export const filterItems = [
  {
    // TODO 根据登录用户回填
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入岗位名称" />,
    span: 4,
  },
  {
    name: 'positionName',
    label: '岗位名称',
    component: <SearchInput placeholder="请输入岗位名称" />,
    span: 4,
  },
  {
    name: 'riskPoints',
    label: '风险点名称',
    component: <SearchInput placeholder="请输入风险点名称" />,
    span: 4,
  },
  {
    name: 'filingDate',
    label: '起止时间',
    component: <FixedWidthRangePicker placeholder={['起始时间', '终止时间']} />,
    span: 4,
    wrapperCol: { span: 4 },
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName' },
  { title: '岗位名称', dataIndex: 'positionName' },
  { title: '风险点', dataIndex: 'riskPoints' },
  { title: '排查时段', dataIndex: 'timeRangeType' },
  { title: '排查人姓名', dataIndex: 'inspectorName' },
  { title: '排查周期', dataIndex: 'executionCycle' },
  { title: '计划状态', dataIndex: 'planStatus' },
  { title: '排查方法', dataIndex: 'checkMethod' },
];
