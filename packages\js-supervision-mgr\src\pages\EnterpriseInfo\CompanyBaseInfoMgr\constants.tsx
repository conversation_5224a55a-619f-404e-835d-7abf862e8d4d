import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '企业基本信息管理';

export const exportUrl = `${SPPREFIX}/basicinformation/enterpriseInformation/exportInfo`;

export const importUrl = `${SPPREFIX}/basicinformation/enterpriseInformation/importInfo`;

export const exportTitle = '企业信息导出';

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    span: 5,
    extraProps: {
      wrapperCol: { span: 24 }, // 设置更大的span值
    },
    component: <SearchInput placeholder="请输入企业名称名称" style={{ width: '100%' }} />,
  },
  {
    name: 'enterpriseCode',
    label: '统一社会信用代码',
    span: 5,
    extraProps: {
      wrapperCol: { span: 24 }, // 设置更大的span值
    },
    component: <SearchInput placeholder="请输入统一社会信用代码" style={{ width: '100%' }} />,
  },
  {
    name: 'industryCategory',
    label: '行业类别',
    span: 5,
    extraProps: {
      wrapperCol: { span: 24 }, // 设置更大的span值
    },
    component: <SearchInput placeholder="请输入行业类别" style={{ width: '100%' }} />,
  },
];

// 绕开试飞代码扫描所以这么写
const BASE_URL = '/storage/api/downloadFile';
const QUERY_PARAMS = new URLSearchParams({
  bucket: 'basicinformation',
  fileName: '企业基本信息管理模板.xlsx',
  objectName: '20250709/879d9e50-606e-43a8-b98f-055177d4fca9.xlsx',
});
const templateDownloadUrl = `${BASE_URL}?${QUERY_PARAMS.toString()}`;

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '导出', ...baseButtonStyle },
  {
    component: <></>,
  },
  {
    text: '模板下载',
    ...baseButtonStyle,
    onClick: () => window.open(templateDownloadUrl, '_blank'),
  },
];

export const tableColumns: any[] = [
  { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true, width: 250 },
  { title: '统一社会信用代码', dataIndex: 'enterpriseCode', width: 200 },
  { title: '行业类别', dataIndex: 'industryCategory', width: 150 },
  { title: '法人', dataIndex: 'legalRepresentative', width: 100 },
  {
    title: '经营期限',
    dataIndex: 'businessPeriod', // 虚拟字段
    width: 200,
    render: (_: any, record: any) => {
      const { businessPeriodStart, businessPeriodEnd } = record;
      // 处理空值情况
      if (!businessPeriodStart && !businessPeriodEnd) return '-';
      if (!businessPeriodStart) return `- ~ ${businessPeriodEnd}`;
      if (!businessPeriodEnd) return `${businessPeriodStart} ~ -`;
      return `${businessPeriodStart} ~ ${businessPeriodEnd}`;
    },
  },
  { title: '注册地址', dataIndex: 'registeredAddress', width: 200 },
];
