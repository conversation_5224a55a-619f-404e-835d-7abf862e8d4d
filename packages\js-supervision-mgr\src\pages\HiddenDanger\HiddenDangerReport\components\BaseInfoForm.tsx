import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { formItems as correctItems } from './CorrectForm';
import imagesImg from '@/assets/images/common/image.png';

const { nameLengthRules, textAreaLengthRules } = RULES;

const showConditionInfo = {
  watchTarget: 'correctionStatus.label',
  matchedValue: '已整改',
};

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
  },
  { quickItemParams: ['排查时间', 'inspectionTime', 'DatePicker', true] },
  { quickItemParams: ['登记人', 'registrant', 'Input', true] },
  { quickItemParams: ['隐患名称', 'dangerName', 'Input', false], rules: [nameLengthRules] },
  {
    quickItemParams: ['隐患级别', 'dangerLevel', 'Input', true],
  },
  {
    quickItemParams: ['隐患类别', 'dangerCategory', 'Select', true],
    optionsServices: () => dictConfigWrapper('danger_category'),
  },
  {
    quickItemParams: ['隐患描述', 'dangerDescription', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['整改措施', 'correctiveAction', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  { quickItemParams: ['整改时限（天）', 'correctionDeadline', 'IntegerInput', true] },
  { quickItemParams: ['整改效果', 'correctionEffect', 'TextArea', false] },
  { quickItemParams: ['整改责任人', 'responsiblePerson', 'Input', false] },
  {
    quickItemParams: ['整改状态', 'correctionStatus', 'Select', false],
    optionsServices: () => dictConfigWrapper('correction_status'),
    selectInfo: {
      onOptionsLoaded: options => options.find(v => v.label === '未整改')?.value,
    },
  },
  { quickItemParams: ['现场照片', 'scenePhotos', 'ImgUpload', false], imgInfo: { imagesImg } },
  { type: 'blank', span: 16 },
  ...correctItems.map(item => ({ ...item, showConditionInfo })),

  { type: 'hideItem', name: 'enterpriseCode', relatedValue: 'enterpriseName.enterpriseCode' },
  { type: 'hideItem', name: 'correctionTime' },
  { type: 'hideItem', name: 'correctionPerson' },
  { type: 'hideItem', name: 'correctionContent' },
  { type: 'hideItem', name: 'afterPhotos' },
];

export const fileNames = ['scenePhotos', 'afterPhotos'];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return <DynamicForm items={formItems} openType={openType} form={form} />;
};

export default BaseInfoForm;
