import { PREFIX_SERVER, FileValueItem } from '../global';

export const codeRules = { pattern: /^[a-zA-Z0-9_-]+$/, message: '编码只能包含数字、字母和下划线' };

export const nameLengthRules = { max: 50, message: '名称不能超过50个字符' };

export const codeLengthRules = { max: 20, message: '编码不能超过20个字符' };

export const textAreaLengthRules = { max: 300, message: '不能超过300个字符' };

export const phoneRules = { pattern: /^\d+$/, message: '联系电话只能包含数字' };

export const phoneLengthRules = { min: 1, max: 15, message: '电话号码长度不超过15位' };

export const emailRules = { type: 'email', message: '请输入有效的邮箱地址!' };

export const postalCodeRules = { pattern: /^\d{6}$/, message: '请输入6位数字的邮政编码' };

export const idRules = { pattern: /^\d{17}[\dXx]$/, message: '请输入正确的身份证号码' };

export const RULES = {
  idRules,
  codeRules,
  phoneRules,
  emailRules,
  postalCodeRules,
  nameLengthRules,
  codeLengthRules,
  phoneLengthRules,
  textAreaLengthRules,
};

type Rule = {
  required?: boolean;
  message?: string;
  [key: string]: any;
};

export function buildRules(message: string, required: boolean, ...otherRules: any[]): Rule[] {
  const baseRule: Rule = { required, message };
  return [baseRule, ...otherRules];
}

export function buildRules2(...otherRules: any[]) {
  return [...otherRules];
}

export const BATCH_IMAGE_NAME = 'attBatchParams';

// 获取 cascadeFieldsMap 所有字符串数组中的所有字段
function getAllCascadeFields(cascadeFieldsMap?: Record<string, string[]>): string[] {
  if (!cascadeFieldsMap) return [];
  return Object.values(cascadeFieldsMap).flat();
}

/**
 * 查找 formValues 中匹配 names 数组及 name_number 格式的键
 * @param formValues 要搜索的对象
 * @param names 要匹配的名称数组
 * @returns 包含所有 names 成员及匹配的 name_number 格式键的数组
 */
function findMatchedKeys(formValues: Record<string, any>, names: string[]): string[] {
  const result = new Set<string>();

  // 首先添加所有 names 中的成员
  for (const name of names) {
    result.add(name);

    // 查找所有 name_number 格式的键
    const regex = new RegExp(`^${name}_\\d+$`);
    for (const key in formValues) {
      if (regex.test(key)) {
        result.add(key);
      }
    }
  }

  return Array.from(result);
}

// 封装 formNames 生成逻辑
export const getFormNames = (
  form: any,
  imageFieldNames: string[],
  batchImgName: string,
  cascadeFieldsMap?: Record<string, string[]>,
): string[] => {
  const filedNames = Object.keys(form.getFieldsValue());
  // 获取所有级联字段
  const cascadeFields = getAllCascadeFields(cascadeFieldsMap);
  let formNames = filedNames.concat(cascadeFields);
  if (batchImgName) {
    formNames = imageFieldNames.concat(formNames, batchImgName);
  }
  // 去重
  return Array.from(new Set(formNames));
};

export const getRecordInitValue = (
  record: any,
  keyMap: string[],
  hasDynamicFormFields: boolean,
) => {
  const result: Record<string, any> = {};
  // 先处理 keyMap 里的普通 key
  for (const key of keyMap) {
    if (record.hasOwnProperty(key)) {
      result[key] = record[key] || '';
    }
  }
  // 如果需要动态字段，额外处理 name_number 格式
  if (hasDynamicFormFields) {
    for (const recordKey of Object.keys(record)) {
      // 判断是否为 name_number 格式
      const match = recordKey.match(/^(.+)_\d+$/);
      if (match) {
        result[recordKey] = record[recordKey] || '';
      }
    }
  }
  return result;
};

/**
 * 处理级联下拉框的存值，将数组值拆分到 formData 的指定字段中
 * @param formData 原始表单数据
 * @param cascadeFieldsMap 级联字段映射关系
 * @returns 新的表单数据对象
 */
export const processCascadeFields = (
  formData: Record<string, any>,
  cascadeFieldsMap?: Record<string, string[]>,
): Record<string, any> => {
  const newFormData: Record<string, any> = { ...formData };
  if (!cascadeFieldsMap) return newFormData;
  Object.entries(cascadeFieldsMap).forEach(([cascadeKey, targetFields]) => {
    const cascadeValue = formData[cascadeKey];
    if (Array.isArray(cascadeValue)) {
      targetFields.forEach((field, idx) => {
        newFormData[field] = cascadeValue[idx] ?? undefined;
      });
      delete newFormData[cascadeKey];
    }
  });
  return newFormData;
};

/**
 * 逆向处理级联下拉框的存值，将 formData 中拆分到各字段的数据还原为级联数组，并删除原有字段
 * @param formData 原始表单数据
 * @param cascadeFieldsMap 级联字段映射关系
 * @returns 还原后的表单数据对象
 */
export const restoreCascadeFields = (
  formData: Record<string, any>,
  cascadeFieldsMap?: Record<string, string[]>,
): Record<string, any> => {
  const newFormData: Record<string, any> = { ...formData };
  if (!cascadeFieldsMap) return newFormData;
  Object.entries(cascadeFieldsMap).forEach(([cascadeKey, targetFields]) => {
    const cascadeArr = targetFields.map(field => newFormData[field]);
    // 只有当至少有一个字段有值时才还原
    if (cascadeArr.some(val => val !== undefined)) {
      newFormData[cascadeKey] = cascadeArr;
      targetFields.forEach(field => {
        delete newFormData[field];
      });
    }
  });
  return newFormData;
};

// 工具函数：处理表单中的图片字段
export const processImageFields = (
  values: any,
  imageFieldNames: string[],
  imageFieldFormatControl: boolean,
  imgStringFormat: boolean,
) => {
  // 浅拷贝一份，避免直接修改 form 中内容
  const newValues = { ...values };
  const newImgFieldNames = findMatchedKeys(values, imageFieldNames);
  newImgFieldNames.forEach(field => {
    const fieldValue = newValues[field];
    if (Array.isArray(fieldValue)) {
      if (imageFieldFormatControl) {
        // 只提取 downloadUrl，组成字符串数组
        newValues[field] = fieldValue
          .map(item => item?.response?.data?.downloadUrl || item)
          .filter(Boolean);
        if (imgStringFormat && newValues[field].length === 1) {
          newValues[field] = newValues[field][0];
        }
      } else {
        // 只保留 downloadUrl、size、name、type
        newValues[field] = fieldValue.map(item => ({
          downloadUrl: item?.url || item?.response?.data?.downloadUrl,
          size: item?.size,
          name: item?.name,
          type: item?.type,
        }));
      }
    }
  });
  return newValues;
};

/**
 * 将表单中图片字段的字符串值转换为数组（逆向imgStringFormat逻辑）
 * @param values 表单值对象
 * @param imageFieldNames 需要处理的图片字段名数组
 * @returns 处理后的新对象
 */
export const convertImageStringToArray = (values: any, imageFieldNames: string[]) => {
  // 浅拷贝避免直接修改原对象
  const newValues = { ...values };

  imageFieldNames.forEach(field => {
    const fieldValue = newValues[field];
    // 只处理字符串类型的值（逆向imgStringFormat）
    if (typeof fieldValue === 'string') {
      newValues[field] = [fieldValue];
    }
  });

  return newValues;
};

/**
 * 处理对象，将指定字段的数组内容转换为 attBatchParams
 * @param {Array} arr 示例：[ {a: 'b'}, {d: 'e'} ]
 * @param {Object} obj 示例：{a: [ {...} ], d: [ {...} ], c: '其余内容'}
 * @returns {Object} 处理后的对象
 */
export function formatFormBatchImages(
  arr: { [props: string]: string }[],
  obj: Record<string, any>,
  batchImgName: string,
) {
  // 复制原对象，避免修改原始数据
  const result = { ...obj };
  // 用于收集所有 attBatchParams
  let attBatchParams: any[] = [];
  arr.forEach(mapItem => {
    const [key, value] = Object.entries(mapItem)[0];
    if (Array.isArray(obj[key])) {
      // 合并所有 attBatchParams
      attBatchParams = attBatchParams.concat(
        obj[key].map((item: any) => ({
          companyPath: item.downloadUrl,
          companyName: item.name,
          companyType: value,
        })),
      );
      // 删除原有的key
      delete result[key];
    }
  });
  if (attBatchParams.length > 0) {
    result[batchImgName] = attBatchParams;
  }
  return result;
}

/**
 * 逆向解析 formatFormBatchImages 处理后的对象
 * @param arr 字段映射数组，格式同 formatFormBatchImages 的 arr
 * @param obj 已经被 formatFormBatchImages 处理过的对象
 * @returns 还原后的对象
 */
export function parseFormBatchImages(
  arr: { [props: string]: string }[],
  obj: Record<string, any>,
  batchImgName: string,
) {
  // 复制原对象，避免修改原始数据
  const result = { ...obj };
  // 获取 attBatchParams
  const attBatchParams: any[] = obj[batchImgName] || [];
  arr.forEach(mapItem => {
    const [key, value] = Object.entries(mapItem)[0];
    // 找出 attBatchParams 中 companyType 匹配的项
    const matchedItems = attBatchParams
      .filter(item => item.companyType === value)
      .map(item => ({
        url: item.companyPath,
        name: item.companyName,
      }));
    // 还原到 result[key]
    result[key] = matchedItems;
  });
  // 删除 attBatchParams 字段
  delete result[batchImgName];
  return result;
}

export type FileUploadValue = string | FileValueItem | Array<string | FileValueItem>;

export const getFileList = (value?: FileUploadValue) => {
  if (!value) return [];
  const arr = Array.isArray(value) ? value : [value];
  return arr
    .filter(Boolean)
    .map((item, idx) => {
      // 新上传图片：包含 originFileObj 字段，直接返回
      if (item && typeof item === 'object' && 'originFileObj' in item) {
        return item;
      }
      let url = '';
      if (typeof item === 'string') {
        url = item;
      } else if (typeof item === 'object' && item.url) {
        const { url: itemUrl } = item;
        url = itemUrl;
      } else {
        return null;
      }
      // 判断是否已经有 PRIVIEW_SERVER 前缀
      const hasPreviewServer = url.startsWith(PREFIX_SERVER);
      const fullUrl = hasPreviewServer ? url : PREFIX_SERVER + url;
      return {
        uid: String(idx),
        name: url.split('/').pop() || `图片${idx + 1}`,
        status: 'done',
        url: fullUrl,
      };
    })
    .filter(Boolean);
};
