.base-info-form {
  .enterprise-info-card {
    margin-bottom: 16px;

    .ant-card-head {
      background: #fafafa;
      border-bottom: 1px solid #e8e8e8;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    // Descriptions 组件样式
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #666;
      }

      .ant-descriptions-item-content {
        color: #262626;
        word-break: break-all;
      }
    }

    // 许可范围特殊样式
    .permit-content {
      color: #262626;
      font-size: 14px;
      line-height: 1.6;
      text-align: justify;
      padding: 8px 0;
    }
  }

  .tabs-card {
    margin-bottom: 16px;

    .ant-card-body {
      padding: 0;
    }

    .ant-tabs {
      .ant-tabs-nav {
        margin-bottom: 0;

        .ant-tabs-tab {
          padding: 16px 24px;
          font-size: 14px;

          .ant-badge {
            .ant-badge-count {
              background: #ff4d4f;
              font-size: 12px;
              height: 18px;
              line-height: 18px;
              min-width: 18px;
              padding: 0 6px;
            }
          }

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: #1890ff;
              font-weight: 500;
            }
          }
        }
      }

      .ant-tabs-content-holder {
        .ant-tabs-content {
          height: auto;

          .ant-tabs-tabpane {
            width: 100%;
            padding: 0 24px;
            box-sizing: border-box;

            // 标签页内的 Descriptions 样式
            .ant-descriptions {
              .ant-descriptions-item-label {
                font-weight: 500;
                color: #666;
              }

              .ant-descriptions-item-content {
                color: #262626;
                word-break: break-all;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .base-info-form {
    .tabs-card {
      .ant-tabs {
        .ant-tabs-nav {
          .ant-tabs-tab {
            padding: 12px 16px;
            font-size: 13px;
          }
        }

        .ant-tabs-content-holder {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              padding: 0 16px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .base-info-form {
    .enterprise-info-card {
      .ant-descriptions {
        // 在小屏幕上强制单列显示
        .ant-descriptions-row {
          .ant-descriptions-item {
            padding-bottom: 12px;
          }
        }
      }
    }

    .tabs-card {
      .ant-tabs {
        .ant-tabs-content-holder {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              padding: 0 12px;
            }
          }
        }
      }
    }
  }
}
