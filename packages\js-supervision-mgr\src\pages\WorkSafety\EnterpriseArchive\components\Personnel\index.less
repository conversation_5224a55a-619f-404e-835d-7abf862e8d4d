.personnel {
  width: calc(100% - 2px);
  padding: 20px 0;
  // .ant-table-thead > tr > th {
  //   background-color: #4a90e2;
  //   color: white;
  //   font-weight: 500;
  //   text-align: center;
  // }

  // .ant-table-tbody > tr > td {
  //   text-align: center;
  // }

  // .ant-descriptions-item-label {
  //   font-weight: 500;
  //   background-color: #f5f5f5;
  // }

  // .ant-checkbox-group {
  //   display: flex;
  //   flex-wrap: wrap;
  //   gap: 8px;
  // }

  // .ant-checkbox-wrapper {
  //   margin-right: 0;
  // }
}
