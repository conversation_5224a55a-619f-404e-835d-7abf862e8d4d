import React, { useState, useEffect } from 'react';
import { FormInstance } from 'antd';
import '../index.less';
import TdTitle from './TdTitle';
import TdInput from './TdInput';
import TdDatePicker from './TdDatePicker';

interface GasAnalysisTableProps {
  form?: FormInstance<any>;
  formInitValue?: any;
}

const CombustibleGas: React.FC<GasAnalysisTableProps> = ({ form, formInitValue }) => {
  const [fields, setFields] = useState({
    sampleOneTime: '',
    sampleTwoTime: '',
    sampleThreeTime: '',
    analysisOneresult: '',
    analysisTworesult: '',
    analysisThreeresult: '',
    analysisPerson: '',
    analysisPoint: '',
  });

  // 监听 formInitValue 变化，重新设置 fields
  useEffect(() => {
    if (formInitValue) {
      setFields({
        sampleOneTime: formInitValue.sampleOneTime,
        sampleTwoTime: formInitValue.sampleTwoTime,
        sampleThreeTime: formInitValue.sampleThreeTime,
        analysisOneresult: formInitValue.analysisOneresult,
        analysisTworesult: formInitValue.analysisTworesult,
        analysisThreeresult: formInitValue.analysisThreeresult,
        analysisPerson: formInitValue.analysisPerson,
        analysisPoint: formInitValue.analysisPoint,
      });
    }
  }, [formInitValue]);

  // 同步到 form
  useEffect(() => {
    form?.setFieldsValue(fields);
  }, [fields, form]);

  // 通用字段更新
  const handleFieldChange = (key: string, value: any) => {
    setFields(prev => ({ ...prev, [key]: value }));
  };

  const bindFieldsEvent = (name: string) => {
    return {
      value: (fields as any)[name],
      onChange: (v: any) => handleFieldChange(name, v),
      placeholder: '请输入',
    };
  };

  return (
    <div className="gasAnalysisTableWrapper">
      <table className="gasAnalysisTable" border={1} cellPadding={8}>
        <tbody>
          <tr>
            <TdTitle title="可燃气体分析(运行的生产装置、区和具有火灾爆炸危险场所)" colSpan={6} />
          </tr>
          <tr>
            <TdTitle title="分析时间" />
            <TdDatePicker
              value={fields.sampleOneTime}
              onChange={(v: any) => handleFieldChange('sampleOneTime', v)}
            />
            <TdDatePicker
              value={fields.sampleTwoTime}
              onChange={(v: any) => handleFieldChange('sampleTwoTime', v)}
            />
            <TdDatePicker
              value={fields.sampleThreeTime}
              onChange={(v: any) => handleFieldChange('sampleThreeTime', v)}
            />
            <TdTitle title="分析点" required />
            <TdInput {...bindFieldsEvent('analysisPoint')} />
          </tr>
          <tr>
            <TdTitle title="可燃气体监测结果" required />
            <TdInput {...bindFieldsEvent('analysisOneresult')} />
            <TdInput {...bindFieldsEvent('analysisTworesult')} />
            <TdInput {...bindFieldsEvent('analysisThreeresult')} />
            <TdTitle title="分析人" />
            <TdInput {...bindFieldsEvent('analysisPerson')} />
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default CombustibleGas;
