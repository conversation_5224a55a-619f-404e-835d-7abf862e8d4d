import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType, SearchInput } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { getriskTypeList } from '@/services/riskCharge/riskType';

const { phoneRules, phoneLengthRules } = RULES;

const phoneBindRules = [phoneLengthRules, phoneRules];

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
  },
  {
    quickItemParams: ['风险点名称', 'riskName', 'ModalSelect', true],
    modalSelectInfo: {
      apiUrl: `${SPPREFIX}/risk/list/riskList`,
      filterItems: [
        {
          name: 'riskName',
          label: '风险点名称',
          component: <SearchInput placeholder="请输入风险点名称" />,
        },
      ],
      width: 700,
      rowSelectType: 'radio' as any,
      valueName: 'riskName',
      columns: [
        { title: '风险点名称', dataIndex: 'riskName', ellipsis: true, width: 250 },
        { title: '风险点级别', dataIndex: 'riskLevel', width: 150 },
      ],
    },
  },
  {
    quickItemParams: ['风险点类型', 'riskType', 'Select', true],
    optionsServices: () => fetchSelectOptions(getriskTypeList, 'typeName', 'typeCode'),
  },
  { quickItemParams: ['风险点级别', 'riskLevel', 'Input', true] },
  { quickItemParams: ['主要后果', 'mainConsequence', 'Input', true] },
  { quickItemParams: ['安全责任人', 'safetyPrincipal', 'Input', true] },
  {
    quickItemParams: ['安全负责人联系方式', 'principalContact', 'Input', false],
    rules: phoneBindRules,
  },
  { quickItemParams: ['风险辨识人员', 'riskIdentifier', 'Input', true] },
  {
    quickItemParams: ['风险辨识人员联系方式', 'identifierContact', 'Input', false],
    rules: phoneBindRules,
  },
  { quickItemParams: ['风险辨识责任人', 'identificationPrincipal', 'Input', true] },
  {
    quickItemParams: ['辨识责任人联系方式', 'principalContact2', 'Input', false],
    rules: phoneBindRules,
  },
  { quickItemParams: ['风险辨识审核人', 'riskReviewer', 'Input', true] },
  { quickItemParams: ['审核人联系方式', 'reviewerContact', 'Input', false], rules: phoneBindRules },
  { quickItemParams: ['风险因子', 'riskFactor', 'Input', true] },
  { quickItemParams: ['风险点场所', 'riskLocation', 'Input', false] },
  { quickItemParams: ['风险点范围JSON', 'riskRange', 'TextArea', false] },
  { type: 'hideItem', name: 'enterpriseCode', relatedValue: 'enterpriseName.enterpriseCode' },
  { type: 'hideItem', name: 'riskType', relatedValue: 'riskName.riskType' },
  { type: 'hideItem', name: 'riskLevel', relatedValue: 'riskName.riskLevel' },
  { type: 'hideItem', name: 'mainConsequence', relatedValue: 'riskName.mainConsequence' },
  { type: 'hideItem', name: 'riskFactor', relatedValue: 'riskName.riskFactor' },
  { type: 'hideItem', name: 'riskCode', relatedValue: 'riskName.riskCode' },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return <DynamicForm items={formItems} openType={openType} form={form} />;
};

export default BaseInfoForm;
