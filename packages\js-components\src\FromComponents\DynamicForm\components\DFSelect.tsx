import { Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { DynamicFormItem } from '../interface';

interface DFSelectProps {
  item: DynamicFormItem;
  placeholder: string;
  value?: any; // 用于受控组件
  formStoreHandle?: (item: DynamicFormItem, option: any) => void;
  onChange?: (value: any) => void; // 用于受控组件
  openType?: string;
}

const DFSelect: React.FC<DFSelectProps> = ({
  item,
  placeholder,
  value,
  onChange,
  formStoreHandle,
  openType,
}) => {
  const { onOptionsLoaded } = item.selectInfo || {};
  const [options, setOptions] = useState(item.options || []);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchOptions = async () => {
      let initOptions: any[] = [];
      if (typeof item.optionsServices === 'function') {
        setLoading(true);
        try {
          const res = await item.optionsServices();
          initOptions = res || [];
          setOptions(initOptions);
        } catch (error) {
          setOptions([]);
          // 这里可以根据需要添加错误提示
        } finally {
          setLoading(false);
        }
      } else {
        initOptions = item.options || [];
        setOptions(item.options || []);
      }
      if (onOptionsLoaded && openType === 'add') onChange?.(onOptionsLoaded(initOptions));
    };

    fetchOptions();
  }, [item.optionsServices, item.options]);

  useEffect(() => {
    if (value && options.length && openType !== 'add') {
      const findOption = options.find(v => v?.value === value) || {};
      formStoreHandle?.(item, findOption);
    }
  }, [value, options]);

  const selectChangeHandle = (value: any, option: any) => {
    formStoreHandle?.(item, option);
    onChange?.(value);
  };

  return (
    <Select
      placeholder={placeholder}
      options={options}
      value={value}
      onChange={selectChangeHandle}
      allowClear
      loading={loading}
    />
  );
};

export default DFSelect;
