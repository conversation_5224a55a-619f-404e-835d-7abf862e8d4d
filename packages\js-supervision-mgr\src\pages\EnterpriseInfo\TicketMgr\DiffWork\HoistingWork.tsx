import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  getLocationItem,
  OpinionItem,
  getLocationColumn,
} from '../constants';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { DynamicFormItem, SearchInput, RULES } from 'jishan-components';
import SafetyMeasuresList from '../components/Measures';
import { getOpinionItems } from '../utils';

const { nameLengthRules } = RULES;

const HomeTitle = '吊装作业';

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  getLocationColumn('吊装地点'),
  { title: '吊物质量', dataIndex: 'liftGearMass', width: 150 },
  { title: '作业级别', dataIndex: 'hoistWorkLevel', width: 220 },
);

const levelItem = {
  name: 'hoistWorkLevel',
  label: '作业级别',
  component: <SearchInput placeholder="请输入作业级别" />,
};

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.workUnit,
  FrequencyItems.applyTime,
  { quickItemParams: ['吊装地点', 'workLocation', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['吊具名称', 'liftGearName', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['吊物内容', 'workContent', 'Input', true] },
  { quickItemParams: ['吊物质量(t)', 'liftGearMass', 'FloatInput', true], decimalPlaces: 3 },
  {
    quickItemParams: ['作业级别', 'hoistWorkLevel', 'Select', true],
    optionsServices: () => dictConfigWrapper('hoist_work_level'),
  },
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  { quickItemParams: ['司索人', 'claimant', 'Input', true] },
  FrequencyItems.supervisor,
  { quickItemParams: ['指挥人员', 'commander', 'Input', true] },
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  FrequencyItems.actualTime,
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  ...getOpinionItems('作业指挥意见', 'commander'),
  ...OpinionItem.unitOpinion,
  ...OpinionItem.departmentOpinion,
  ...OpinionItem.auditDepartmentOpinion,
  ...OpinionItem.completedOpinion,
  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'blindDrawingName' },
];

const HoistingWork: React.FC = () => {
  const configMap = {
    hoistWorkLevel: () => dictConfigWrapper('hoist_work_level'),
  };

  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[getLocationItem('吊装地点'), applyTimeItem, levelItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      configMap={configMap}
      formItems={formItems}
      keyName={HomeTitle}
      x={1550}
    />
  );
};

export default HoistingWork;
