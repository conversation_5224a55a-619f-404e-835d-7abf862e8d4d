import React, { useState, useEffect } from 'react';
import { FormInstance } from 'antd';
import '../index.less';
import TdTitle from './TdTitle';
import TdInput from './TdInput';
import TdDatePicker from './TdDatePicker';

interface GasAnalysisTableProps {
  form?: FormInstance<any>;
  formInitValue?: any;
}

const BlindParams: React.FC<GasAnalysisTableProps> = ({ form, formInitValue }) => {
  const [fields, setFields] = useState({
    pipelineName: '',
    pipelineMedium: '',
    pipelineTemperature: '',
    pipelineStress: '',
    pipelineMaterial: '',
    pipelineSpecification: '',
    pipelineNumber: '',
    actualWorkTime: '',
  });

  // 监听 formInitValue 变化，重新设置 fields
  useEffect(() => {
    if (formInitValue) {
      setFields({
        pipelineName: formInitValue.pipelineName,
        pipelineMedium: formInitValue.pipelineMedium,
        pipelineTemperature: formInitValue.pipelineTemperature,
        pipelineStress: formInitValue.pipelineStress,
        pipelineMaterial: formInitValue.pipelineMaterial,
        pipelineSpecification: formInitValue.pipelineSpecification,
        pipelineNumber: formInitValue.pipelineNumber,
        actualWorkTime: formInitValue.actualWorkTime,
      });
    }
  }, [formInitValue]);

  // 同步到 form
  useEffect(() => {
    form?.setFieldsValue(fields);
  }, [fields, form]);

  // 通用字段更新
  const handleFieldChange = (key: string, value: any) => {
    setFields(prev => ({ ...prev, [key]: value }));
  };

  const bindFieldsEvent = (name: string) => {
    return {
      value: (fields as any)[name],
      onChange: (v: any) => handleFieldChange(name, v),
      placeholder: '请输入',
    };
  };

  return (
    <div className="gasAnalysisTableWrapper">
      <table className="gasAnalysisTable" border={1} cellPadding={8}>
        <tbody>
          <tr>
            <TdTitle title="设备、管道名称" span={2} />
            <TdTitle title="管道参数" colSpan={3} />
            <TdTitle title="盲板参数" colSpan={3} />
            <TdTitle title="实际作业开始时间" span={2} />
          </tr>
          <tr>
            <TdTitle title="介质" />
            <TdTitle title="温度" />
            <TdTitle title="压力" />
            <TdTitle title="材质" />
            <TdTitle title="规格" />
            <TdTitle title="编号" />
          </tr>
          <tr>
            <TdInput {...bindFieldsEvent('pipelineName')} />
            <TdInput {...bindFieldsEvent('pipelineMedium')} />
            <TdInput {...bindFieldsEvent('pipelineTemperature')} />
            <TdInput {...bindFieldsEvent('pipelineStress')} />
            <TdInput {...bindFieldsEvent('pipelineMaterial')} />
            <TdInput {...bindFieldsEvent('pipelineSpecification')} />
            <TdInput {...bindFieldsEvent('pipelineNumber')} />
            <TdDatePicker
              value={fields.actualWorkTime}
              onChange={(v: any) => handleFieldChange('actualWorkTime', v)}
            />
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default BlindParams;
