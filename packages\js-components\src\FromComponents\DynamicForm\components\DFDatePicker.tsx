import React, { useEffect } from 'react';
import { DatePicker } from 'antd';
import { DynamicFormItem } from '../interface';
import dayjs from 'dayjs';

interface DFDatePickerProps {
  item: DynamicFormItem;
  placeholder?: string;
  value?: any; // 接收 Form.Item 灌注下来的 value
  onChange?: (value: any) => void; // 接收 Form.Item 灌注下来的 onChange
}

const DFDatePicker: React.FC<DFDatePickerProps> = ({ item, placeholder, value, onChange }) => {
  const { showTime, initValue } = item.datePickerInfo || {};
  const handle = (v: any) => {
    onChange?.(dayjs(v).format(showTime?.format || 'YYYY-MM-DD'));
  };
  useEffect(() => {
    if (initValue) handle(initValue);
  }, []);

  return (
    <DatePicker
      showTime={showTime}
      placeholder={placeholder}
      style={{ width: '100%' }}
      value={value ? dayjs(value) : undefined}
      onChange={handle}
    />
  );
};

export default DFDatePicker;
