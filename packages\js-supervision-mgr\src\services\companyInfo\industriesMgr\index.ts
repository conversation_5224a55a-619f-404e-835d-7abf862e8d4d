import { request } from '@/utils/net';

export async function saveIndustries(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/changeIndustries/saveIndustries`, params);
}

export async function getIndustriesDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/changeIndustries/industriesDetail`, {
    id,
  });
}

export async function deleteIndustries(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/changeIndustries/removeIndustries`, params);
}

export async function getIndustriesTreeList(subCode: string) {
  return request.get(`${SPPREFIX}/risk/type/industriesTreeList`, { subCode });
}
