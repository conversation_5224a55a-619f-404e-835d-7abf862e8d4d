import React, { useEffect, useState } from 'react';
import { TreeSelect } from 'antd';
import { DynamicFormItem } from '../interface';

interface DFSelectTreeProps {
  item: DynamicFormItem;
  placeholder: string;
  value?: any;
  onChange?: (value: any) => void;
  openType?: string;
  formStoreHandle?: (item: any, option: any) => void;
}
const DFSelectTree: React.FC<DFSelectTreeProps> = ({
  item,
  placeholder,
  value,
  onChange,
  openType,
  formStoreHandle,
}) => {
  const { treeOptions, treeOptionsService } = item.treeSelectInfo || {};
  const [treeData, setTreeData] = useState<any[]>(treeOptions || []);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const fetchTreeOptions = async () => {
      let initTreeOptions: any[] = [];
      if (typeof treeOptionsService === 'function') {
        setLoading(true);
        try {
          const res = await treeOptionsService();
          console.log('res: ', res);
          initTreeOptions = res || [];
          setTreeData(initTreeOptions);
        } catch (error) {
          setTreeData([]);
        } finally {
          setLoading(false);
        }
      } else {
        initTreeOptions = treeOptions || [];
        setTreeData(initTreeOptions);
      }
    };
    fetchTreeOptions();
  }, [treeOptionsService, treeOptions]);

  useEffect(() => {
    if (value && treeData.length && openType !== 'add') {
      // 递归查找选中的节点
      const findNode = (nodes: any[]): any => {
        for (const node of nodes) {
          if (node.value === value) return node;
          if (node.children) {
            const found = findNode(node.children);
            if (found) return found;
          }
        }
        return {};
      };
      const selectedNode = findNode(treeData);
      formStoreHandle?.(item, selectedNode);
    }
  }, [value, treeData]);

  const treeSelectChangeHandle = (value: any, _label: any, extra: any) => {
    formStoreHandle?.(item, extra.triggerNode);
    onChange?.(value);
  };

  return (
    <TreeSelect
      placeholder={placeholder}
      treeData={treeData}
      value={value}
      onChange={treeSelectChangeHandle}
      allowClear
      loading={loading}
      treeDefaultExpandAll
      showSearch
      style={{ width: '100%' }}
    />
  );
};
export default DFSelectTree;
