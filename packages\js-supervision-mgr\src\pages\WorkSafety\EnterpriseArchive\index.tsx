import React, { useState } from 'react';
import { message } from 'antd';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import StatisticsCards, { StatisticItem } from './components/StatisticsCards';
import { tableColumns, HomeTitle, filterItems } from './constants';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { getCertificateDetail } from '@/services/certificateMgr';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const EnterpriseArchive: React.FC = () => {
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [columns, setColumns] = useState([...tableColumns]);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    turnToListPage,
    detailHandler,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(setTableFilters, setColumns, {
    hazardLevel: () => dictConfigWrapper('hazard_level'),
    hazardType: () => dictConfigWrapper('hazard_type'),
  });

  const handleBusinessTermTime = (params: any) => {
    const { businessTermDate, ...rest } = params;
    let validityStart;
    let validityEnd;
    if (Array.isArray(businessTermDate) && businessTermDate.length === 2) {
      validityStart = businessTermDate[0]?.format('YYYY-MM-DD');
      validityEnd = businessTermDate[1]?.format('YYYY-MM-DD');
    }
    return {
      ...rest,
      validityStart,
      validityEnd,
    };
  };

  // 统计卡片点击处理
  const handleStatisticsCardClick = (cardId: string, item: StatisticItem) => {
    message.info(`点击了统计项: ${item.title}，数量: ${item.count}，所属卡片: ${cardId}`);
    // 这里可以根据不同的卡片类型和统计项进行相应的操作
    // 比如跳转到对应的详情页面或者筛选数据
    console.log('卡片ID:', cardId, '统计项:', item);
  };

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getCertificateDetail);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      width: 100,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openDetail(record)}>查看</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      formComp={<BaseInfoForm />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      {/* 统计卡片 */}
      <div style={{ marginBottom: '16px' }}>
        <StatisticsCards onCardClick={handleStatisticsCardClick} />
      </div>

      <div style={{ height: 'calc(100vh - 345px)' }}>
        <ListRequest
          apiUrl={`${SPPREFIX}/safetyProduct/oneCompanyOneFile/list`}
          filterItems={[...tableFilters]}
          updateTrigger={updateTrigger}
          searchParamsHandle={handleBusinessTermTime}
          columns={newColumns}
          inlineButtons
          buttonCol={1}
          labelCol={{ span: 8 }}
          scrollYDelta={48}
          scroll={{ x: 1400 }}
        />
      </div>
    </CommonPage>
  );
};

export default EnterpriseArchive;
