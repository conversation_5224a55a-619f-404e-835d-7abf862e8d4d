import React from 'react';
import { Button } from 'antd';
import { PaperClipOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons';
import { getCompName } from '../../utils/commonFunction';
import './index.less';
import { PRIVIEW_SERVER } from '../../global';

interface FileItemProps {
  value?: string;
  fileName: string;
  onDelete: () => void;
}

const OnlyReadUpload: React.FC<FileItemProps> = ({ value, fileName, onDelete }) => {
  return (
    <div className={getCompName('only-read-upload')}>
      <PaperClipOutlined className="file-icon" />
      <span className="file-name">{fileName}</span>
      <div className="file-actions">
        <a href={`${PRIVIEW_SERVER}${value}`} download>
          <Button type="link" icon={<DownloadOutlined className="btn-icon" />} />
        </a>
        <Button type="link" icon={<DeleteOutlined className="btn-icon" />} onClick={onDelete} />
      </div>
    </div>
  );
};
export default OnlyReadUpload;
