import dayjs from 'dayjs';
import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import FromInfo, { imgNames } from './components/FormInfo';
import ImportDropdownButton from './components/ImportDropdownButton';
import { ListRequest, CommonLinkButton, JSCUtils } from 'jishan-components';
import { deletePerson, getPersonDetail, savePerson } from '@/services/companyInfo/workerMgr';
import styles from './index.less';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const { flattenArrayObjects, unflattenArrayObjects, splitRangeToStartEnd, mergeStartEndToRange } =
  JSCUtils;

interface PersonMgrProps {
  homeTitle: string;
  tableColumns: any[];
  filterItems: any[];
  customButtons: any[];
  BaseInfoForm: React.FC<any>;
  x?: number;
}

const CommonPersonPage: React.FC<PersonMgrProps> = ({
  homeTitle,
  tableColumns,
  filterItems,
  customButtons,
  BaseInfoForm,
  x = 1700,
}) => {
  const [cardCount, setCardCount] = useState(1);

  const {
    updateTrigger,
    setUpdateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    selectedKeys,
    saveHandler,
    editHandler,
    detailHandler,
    deleteHandler,
  } = useCommonFormPage({ HomeTitle: homeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[2].component = <ImportDropdownButton setUpdateTrigger={setUpdateTrigger} />;

  customButtons[1].onClick = () => {
    deleteHandler(selectedKeys, deletePerson);
  };

  const saveProcessInfo = async (formData: any) => {
    const values = await saveHandler(formData, savePerson, data => {
      let newData = { ...data };
      // 处理 rangeDate
      newData = splitRangeToStartEnd(newData, 'validDate', 'validStartDate', 'validEndDate');
      // 将拓展的字段集中到一个字段中
      newData = unflattenArrayObjects(newData, 'attBatchParams');
      return newData;
    });
    return values;
  };

  const detailDataHandler = (data: any) => {
    const { attBatchParams } = data;
    setCardCount(attBatchParams?.length || 1);
    let initValues = { ...data };
    initValues = flattenArrayObjects(initValues, 'attBatchParams');
    initValues = mergeStartEndToRange(initValues, 'validDate', 'validStartDate', 'validEndDate');
    return initValues;
  };

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getPersonDetail, detailDataHandler);
  };

  const handleDeleteOne = (record: any) => {
    deleteHandler([record.id], deletePerson);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getPersonDetail, detailDataHandler);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => handleDeleteOne(record)}>删除</CommonLinkButton>
          <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={saveProcessInfo}
      formComp={
        <FromInfo
          BaseInfoForm={BaseInfoForm}
          openType={openType}
          cardCount={cardCount}
          initValues={initValue}
        />
      }
      formFieldOptions={{
        imageFieldNames: imgNames,
        imgStringFormat: true,
        hasDynamicFormFields: true,
      }}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/personMsg/personList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        rowClassName={record => {
          // 取出 attBatchParams 的有效期和应复审日期
          const validEndDate = record?.attBatchParams?.[0]?.validEndDate;
          const reviewDueDate = record?.attBatchParams?.[0]?.reviewDueDate;
          const now = dayjs();
          // 判断是否过期
          if (
            (validEndDate && dayjs(validEndDate).isBefore(now, 'day')) ||
            (reviewDueDate && dayjs(reviewDueDate).isBefore(now, 'day'))
          ) {
            return styles['row-expired']; // 这里是你自定义的 class
          }
          return '';
        }}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
        scroll={{ x }}
      />
    </CommonPage>
  );
};

export default CommonPersonPage;
