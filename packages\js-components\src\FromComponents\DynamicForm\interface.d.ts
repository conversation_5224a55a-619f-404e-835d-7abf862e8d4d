import { Dayjs } from 'dayjs';
import { ListRequestProps } from '../../ListComponents/ListRequest';

export type CascaderOption = {
  value: string;
  label: string;
  children?: CascaderOption[];
};

export type DynamicFormItemType =
  | 'hideItem'
  | 'Input'
  | 'Select'
  | 'Radio'
  | 'IntegerInput'
  | 'FloatInput'
  | 'TextArea'
  | 'DatePicker'
  | 'RangePicker'
  | 'Cascader'
  | 'blank'
  | 'ImgUpload'
  | 'ModalSelect'
  | 'TreeSelect'
  | 'Sign'
  | 'FileUpload';

export type QuickItemParamsType =
  | [string, string, DynamicFormItemType, boolean, string]
  | [string, string, DynamicFormItemType, boolean];

export interface FormItemParams {
  placeholder: string;
  disabled?: boolean;
  openType?: string;
}

export type WatchInfoType = {
  watchTarget: string;
  matchedValue: any;
};

export type TreeSelectItem = {
  title: string;
  value: any;
  children?: TreeSelectItem[];
};

export interface DynamicFormItem {
  label?: 'hideItem' | 'blank' | string;
  customLabel?: React.ReactNode;
  flexLabel?: boolean; //  是否启用灵活 label。启用后将覆盖默认的 label 样式，使 customLabel 的样式更灵活
  name?: string; // 允许空字符串或 'blank'
  span?: number;
  disabled?: boolean; // 控制禁用
  type?: DynamicFormItemType;
  options?: { label: string; value: string | number }[]; // 仅 Select, Radio 用
  rules?: any[];
  placeholder?: string;
  labelCol?: any;
  initialValue?: any;
  quickItemParams?: QuickItemParamsType;
  relatedValue?: string; // 关联的值，会从 formStore 中依次获取，若格式如：A.B.C，则会按照对象的属性一直取下去
  extraContent?: React.ReactNode; // 额外内容，如按钮等
  decimalPlaces?: number; // 控制小数位数，仅 FloatInput 用
  cascaderOptions?: CascaderOption[]; // 级联选择数据，用于 Cascader 类型
  component?: React.ReactNode; // 若有则作为 Form.Item 的内容渲染
  optionsServices?: () => Promise<{ label: string; value: string | number }[]>; // 异步获取 options 选项
  cascaderOptionsServices?: () => Promise<CascaderOption[]>; // 异步获取 Cascader 选项
  showConditionInfo?: WatchInfoType;
  treeSelectInfo?: {
    treeOptionsService?: () => Promise<TreeSelectItem[]>;
    treeOptions?: TreeSelectItem[];
  };
  selectInfo?: {
    onOptionsLoaded?: (options: any[]) => void;
  };
  radioInfo?: {
    onOptionsLoaded?: (options: any[]) => void;
  };
  datePickerInfo?: {
    showTime?: { format: string };
    initValue?: Dayjs;
  };
  rangePickerInfo?: {
    showTime?: { format: string };
    initValue?: [Dayjs | undefined, Dayjs | undefined];
    placeholder?: [string, string];
  };
  imgInfo?: {
    imagesImg?: any;
    buttonTitle?: string; // 默认为 '上传附件'
    maxNum?: number; // 默认为 1
    alt?: string; // 默认为 '图片'
    emptyText?: string; // 默认为 '未上传图片'
  };
  uploadFileInfo?: {
    targetType?: string;
    buttonTitle?: string;
    emptyText?: string;
    maxNum?: number;
    tip?: React.ReactNode;
    maxFileSize?: number;
  };
  signInfo?: {
    signCallback?: (form: FormInstance<any> | undefined, url: string) => void;
  };
  modalSelectInfo?: ListRequestProps & {
    width?: number;
    valueName?: string;
    relatedSearchParams?: string[];
  };
}
