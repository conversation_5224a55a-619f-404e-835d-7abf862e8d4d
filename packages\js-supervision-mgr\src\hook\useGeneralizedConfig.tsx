import React, { useEffect, useState } from 'react';
import { createCodeLabelMap } from '@/utils/commonFunction';
import { FixedWidthSelect } from 'jishan-components';

type Option = { label: string; value: any };
type ConfigMap = {
  [key: string]: () => Promise<Option[]>;
};

export const useGeneralizedConfig = (
  setFormFilters: React.Dispatch<React.SetStateAction<any[]>> | null,
  setColumns: React.Dispatch<React.SetStateAction<any[]>> | null,
  configMap: ConfigMap,
) => {
  // 存储每个key对应的options
  const [optionsMap, setOptionsMap] = useState<{ [key: string]: Option[] }>({});

  // 拉取所有options
  useEffect(() => {
    Object.entries(configMap).forEach(async ([key, fetchFn]) => {
      const opts = await fetchFn();
      setOptionsMap(prev => ({ ...prev, [key]: opts }));
    });
  }, []);

  // 更新columns的render
  useEffect(() => {
    // 更新formFilters为下拉框
    setFormFilters?.(prevFormFilters => {
      if (!prevFormFilters || prevFormFilters.length === 0) return prevFormFilters;
      let changed = false;
      const newFormFilters = prevFormFilters.map(filter => {
        if (configMap[filter.name]) {
          const opts = optionsMap[filter.name] || [];
          changed = true;
          return {
            ...filter,
            component: (
              <FixedWidthSelect
                placeholder={`请选择${filter.label || ''}`}
                allowClear
                options={opts}
              />
            ),
          };
        }
        return filter;
      });
      return changed ? newFormFilters : prevFormFilters;
    });

    setColumns?.(prevColumns => {
      if (!prevColumns || prevColumns.length === 0) return prevColumns;

      let changed = false;
      const newColumns = prevColumns.map(col => {
        if (configMap[col.dataIndex]) {
          const opts = optionsMap[col.dataIndex] || [];
          if (opts.length > 0) {
            const map = createCodeLabelMap(opts);
            changed = true;
            return {
              ...col,
              renderTooltip: (code: string) => map[code],
              render: (code: string) => <span>{map[code] || code}</span>,
            };
          }
        }
        return col;
      });

      return changed ? newColumns : prevColumns;
    });
  }, [optionsMap]);

  return {
    optionsMap,
  };
};
