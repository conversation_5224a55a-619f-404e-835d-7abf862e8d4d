import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import {
  tableColumns,
  HomeTitle,
  filterItems,
  customButtons,
  exportTitle,
  exportUrl,
} from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { deleteRiskList, getRiskListDetail, saveRiskList } from '@/services/riskCharge/riskList';
import { getriskTypeList } from '@/services/riskCharge/riskType';
import { useSearchParams } from '@umijs/max';
import { FormInstance } from 'antd';

const RiskList: React.FC = () => {
  const [columns, setColumns] = useState([...tableColumns]);
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [searchParams] = useSearchParams();

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    setFormValues,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    detailHandler,
    getUploadButton,
    selectedRowsDel,
    setExportEvent,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(setTableFilters, setColumns, {
    riskType: () => fetchSelectOptions(getriskTypeList, 'typeName', 'typeCode'),
  });

  const typeCode = searchParams.get('typeCode');
  let newCustomButtons: any[] = [];

  if (!typeCode) {
    newCustomButtons = customButtons;
  }

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteRiskList);
  };

  customButtons[2].component = getUploadButton(`${SPPREFIX}/risk/list/importRiskList`);

  customButtons[3].onClick = setExportEvent(exportUrl, exportTitle);

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getRiskListDetail);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getRiskListDetail);
  };

  const didType = (values: any, form: FormInstance<any> | null) => {
    if (typeCode) {
      const newValues = { ...values };
      newValues.riskType = typeCode;
      form?.setFieldValue('riskType', typeCode);
      return newValues;
    }
    return values;
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => {
        return (
          <div>
            {!typeCode && (
              <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            )}
            <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveRiskList)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/risk/list/riskList`}
        getFormValues={setFormValues}
        filterItems={[...tableFilters]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        searchParamsHandle={didType}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={newCustomButtons}
        inlineButtons
        buttonCol={1}
        scrollYDelta={48}
        scroll={{ x: 1500 }}
      />
    </CommonPage>
  );
};

export default RiskList;
