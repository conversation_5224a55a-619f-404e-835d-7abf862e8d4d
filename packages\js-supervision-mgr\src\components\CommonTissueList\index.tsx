import React, { useEffect, useState } from 'react';
import { Tree } from 'antd';
import { FolderFilled, ApartmentOutlined } from '@ant-design/icons';

export interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
  icon?: React.ReactNode;
}

interface TissueListProps {
  treeData: TreeNode[];
  onSelect?: (orgCode: string[]) => void;
  dataSetHandle?: (list: TreeNode[]) => void;
  focusNode?: string;
}

const CommonTissueList: React.FC<TissueListProps> = ({
  onSelect,
  dataSetHandle,
  treeData,
  focusNode,
}) => {
  const [renderData, setRenderData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const expandToLeafNode = (nodes: TreeNode[]): string[] => {
    const keys: string[] = [];
    const traverse = (node: TreeNode) => {
      keys.push(node.key);
      if (node.children && node.children.length > 0) {
        traverse(node.children[0]); // 继续展开第一个子节点
      }
    };
    if (nodes.length > 0) {
      traverse(nodes[0]); // 从第一个根节点开始
    }
    return keys;
  };

  useEffect(() => {
    const transformData = (data: any[]): TreeNode[] => {
      return data.map((item) => ({
        title: item.orgName,
        key: item.orgId,
        icon:
          item.icon ||
          (item.children.length ? (
            <FolderFilled style={{ color: '#5B85FA' }} />
          ) : (
            <ApartmentOutlined style={{ color: '#4DD4E8' }} />
          )),
        children: item.children.length ? transformData(item.children) : [],
      }));
    };
    const formattedData = transformData(treeData);
    setRenderData(formattedData);
    if (dataSetHandle) dataSetHandle(formattedData);

    // 设置展开直到叶子节点
    const keysToExpand = expandToLeafNode(formattedData);
    setExpandedKeys(keysToExpand);

    if (!focusNode && formattedData[0]?.key) {
      setSelectedKeys([formattedData[0].key]);
      if (onSelect) onSelect([formattedData[0].key]);
    }
  }, [treeData]);

  const selectHandle = (row: any) => {
    setSelectedKeys(row);
    if (onSelect) onSelect(row);
  };

  const onExpand = (keys: string[]) => {
    setExpandedKeys(keys); // 更新展开的节点
  };

  useEffect(() => {
    if (focusNode) setSelectedKeys([focusNode]);
  }, [focusNode]);

  return (
    <div style={{ height: 'calc(100% - 30px)', overflow: 'auto' }}>
      <Tree
        treeData={renderData}
        selectedKeys={selectedKeys}
        expandedKeys={expandedKeys}
        showIcon
        selectable
        onSelect={selectHandle}
        // @ts-ignore
        onExpand={onExpand} // 添加 onExpand 回调
      />
    </div>
  );
};

export default CommonTissueList;
