import { request } from '@/utils/net';

export async function saveTicket(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/special/saveTicket`, params);
}

export async function getTicketDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/special/ticketDetail`, {
    id,
  });
}

export async function deleteTicket(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/special/removeTicket`, params);
}

export async function getTicketAttachAddr(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/special/ticketBillDownload`, {
    id,
  });
}
