import React from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm, { fileNames } from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

import { deleteMeasure, getMeasureDetail, saveMeasure } from '@/services/riskLevelMgr/measureList';

const MeasureList: React.FC = () => {
  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    setFormValues,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    selectedRowsDel,
    deleteHandler,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteMeasure);
  };

  const openEdit = (record: any) => {
    editHandler(record, getMeasureDetail);
  };

  const handleDeleteOne = (record: any) => {
    deleteHandler([record.id], deleteMeasure);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            <CommonLinkButton onClick={() => handleDeleteOne(record)}>删除</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveMeasure)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
      formFieldOptions={{
        normalFileNames: fileNames,
        fileStringFormat: true,
      }}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/riskLevel/control/measureList`}
        getFormValues={setFormValues}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scroll={{ x: 2800 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default MeasureList;
