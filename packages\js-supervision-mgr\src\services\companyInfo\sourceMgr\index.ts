import { request } from '@/utils/net';

export async function saveSource(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/sourceManagement/saveSource`, params);
}

export async function getSourceDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/sourceManagement/sourceDetail`, {
    id,
  });
}

export async function deleteSource(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/sourceManagement/removeSource`, params);
}
