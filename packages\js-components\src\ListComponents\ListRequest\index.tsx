import React, { useState, useEffect, useRef } from 'react';
import RequestTable from '../RequestTable';
import { ColProps, FormInstance, message } from 'antd';
// @ts-ignore
import { request } from 'umi';
import FilterForm, { CustomButton, FormItem } from '../../FromComponents/FilterForm';

// 封装处理请求参数的函数
const getRequestOptions = (
  method: 'GET' | 'POST',
  params: any,
  extraParams: Record<string, any> | undefined,
  currentPage: number,
  pageSize: number,
) => {
  const commonParams = {
    ...Object.assign({ ...params }, { ...extraParams }),
    pageNo: currentPage,
    pageSize,
  };
  return {
    method,
    ...(method === 'POST' ? { data: commonParams } : { params: commonParams }),
  };
};

type VoidFn = () => void;

interface RefComp {
  resetHandle: VoidFn;
}

export interface ListRequestProps {
  apiUrl: string;
  filterItems: Array<FormItem>;
  columns: any[];
  updateTrigger?: any; // 控制更新触发
  customTable?: React.ReactNode;
  inlineButtons?: boolean;
  extraParams?: Record<string, any>;
  pageSize?: number; // 控制每页展示的条数
  customButtons?: CustomButton[];
  method?: 'GET' | 'POST'; // 新增的 method 属性
  buttonCol?: number;
  tableClass?: string;
  scroll?: { x?: number; y?: number };
  showSizeChanger?: boolean;
  searchButtonStyle?: React.CSSProperties;
  labelCol?: ColProps;
  scrollYDelta?: number;
  searchParamsHandle?: (params: any, form: FormInstance<any> | null) => any;
  customCompBeforForm?: any;
  comBeforeFormCol?: number;
  tableBordered?: boolean;
  rowSelection?: boolean;
  rowKey?: string;
  footerComp?: any;
  rowSelectType?: 'checkbox' | 'radio';
  getFormValues?: React.Dispatch<React.SetStateAction<Record<string, any>>>;
  onRowSelect?: (selectedRowKeys: React.Key[], selectedRows: any[]) => void;
  serviceResHandle?: (res: any) => any;
  beforUpdate?: (resetHook?: () => void) => boolean;
  hideActionButtons?: boolean;
  rowClassName?: (record: any, index: number) => string;
  descriptionComponent?: React.ReactNode; // 在FilterForm和RequestTable之间显示的描述信息组件
}

const ListRequest: React.FC<ListRequestProps> = ({
  apiUrl,
  filterItems,
  columns,
  customTable,
  inlineButtons,
  customButtons,
  extraParams,
  pageSize = 10,
  updateTrigger,
  buttonCol,
  tableClass,
  method = 'GET', // 默认值为 'GET'
  scroll,
  labelCol,
  rowKey,
  footerComp,
  showSizeChanger,
  searchButtonStyle,
  scrollYDelta,
  customCompBeforForm,
  comBeforeFormCol,
  tableBordered,
  rowSelection,
  onRowSelect,
  rowClassName,
  beforUpdate,
  getFormValues,
  searchParamsHandle = (params, _) => params,
  serviceResHandle = res => res,
  hideActionButtons,
  rowSelectType,
  descriptionComponent,
}) => {
  const [data, setData] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [tableLoading, setTableLoading] = useState(false);
  const [tablePageSize, setTablePageSize] = useState(pageSize);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});
  const [isInitialized, setIsInitialized] = useState(false);
  const [filterForm, setFilterForm] = useState<FormInstance<any> | null>(null);

  const filterFormRef = useRef<RefComp>(null);

  const fetchData = async (params: any = {}) => {
    setTableLoading(true);
    try {
      const requestOptions = getRequestOptions(
        method,
        params,
        extraParams,
        currentPage,
        tablePageSize,
      );
      const response = await request(apiUrl, requestOptions);
      const data = serviceResHandle(response.data);
      setData([]);
      setTimeout(() => {
        setData(data?.data || []);
        setTotal(data?.totalRecords || 0);
      });
    } catch (error) {
      message.error('请求数据时发生错误，请稍后重试');
    } finally {
      setTableLoading(false);
    }
  };

  const setPageInfo = (index: number, size: number) => {
    setCurrentPage(index);
    setTablePageSize(size);
  };

  const refresh = () => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    } else {
      const finalParams = searchParamsHandle(filterValues, filterForm);
      if (finalParams === false) return;
      fetchData(finalParams);
    }
  };

  const handleSearch = (values: any, form: FormInstance<any>) => {
    setFilterValues(values);
    setFilterForm(form);
  };

  const handleReset = () => {
    setFilterValues({});
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  useEffect(() => {
    const finalParams = searchParamsHandle(filterValues, filterForm);
    if (finalParams === false) return;
    fetchData(finalParams);
  }, [currentPage, tablePageSize]);

  useEffect(() => {
    if (isInitialized) refresh();
  }, [filterValues]);

  useEffect(() => {
    if (isInitialized) {
      let updateFlag = true;
      if (beforUpdate) updateFlag = beforUpdate(filterFormRef.current?.resetHandle);
      updateFlag && refresh();
    } else {
      setIsInitialized(true);
    }
  }, [updateTrigger]);

  return (
    <div style={{ height: '100%' }}>
      <FilterForm
        onSearch={handleSearch}
        inlineButtons={inlineButtons}
        onReset={handleReset}
        filterItems={filterItems}
        customButtons={customButtons}
        buttonCol={buttonCol}
        searchButtonStyle={searchButtonStyle}
        labelCol={labelCol}
        ref={filterFormRef}
        getFormValues={getFormValues}
        comBeforeFormCol={comBeforeFormCol}
        customCompBeforForm={customCompBeforForm}
        hideActionButtons={hideActionButtons}
      />
      {descriptionComponent && descriptionComponent}
      <RequestTable
        rowKey={rowKey}
        scroll={scroll}
        bordered={tableBordered}
        showSizeChanger={showSizeChanger}
        columns={columns}
        rowClassName={rowClassName}
        dataSource={data}
        customTable={customTable}
        tableLoading={tableLoading}
        tableClass={tableClass}
        scrollYDelta={scrollYDelta}
        rowSelection={rowSelection}
        onRowSelect={onRowSelect}
        setPageInfo={setPageInfo}
        footerComp={footerComp}
        rowSelectType={rowSelectType}
        pagination={{
          current: currentPage,
          pageSize: tablePageSize,
          total,
          onChange: handlePageChange,
        }}
      />
    </div>
  );
};

export default ListRequest;
