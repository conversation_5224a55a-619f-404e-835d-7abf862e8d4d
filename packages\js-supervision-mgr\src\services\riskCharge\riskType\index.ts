import { request } from '@/utils/net';

export async function getriskTypeList() {
  return request.get(`${SPPREFIX}/risk/type/riskTypeList?pageNo=1&pageSize=10000`);
}

export async function saveRiskType(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/risk/type/saveRiskType`, params);
}

export async function getRiskTypeDeatil(id: number) {
  return request.get(`${SPPREFIX}/risk/type/riskTypeDetail`, {
    id,
  });
}

export async function deleteRiskType(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/risk/type/removeRiskType`, params);
}
