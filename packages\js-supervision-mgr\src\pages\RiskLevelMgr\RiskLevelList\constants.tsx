import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';
import { getLevelFieldsOptions } from '@/services/riskCharge/riskPoint';
import { fetchSelectOptions } from '@/utils/commonFunction';

export const HomeTitle = '风险类型';

export const levelWarnTip = '请点击开始分析按钮，分析风险值后会自动回填';

export const exportUrl = `${SPPREFIX}/riskLevel/scaleList/exportAssessment`;

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入风险类型名称" />,
  },
  {
    name: 'riskLevel',
    label: '风险等级',
    component: <SearchInput placeholder="请输入所属行业" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '下载', ...baseButtonStyle },
  { text: '打印', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName' },
  { title: '事故发生的可能性', dataIndex: 'accidentProbability' },
  { title: '频繁程度', dataIndex: 'accidentFrequency' },
  { title: '事故产生的后果', dataIndex: 'accidentConsequence' },
  { title: '评估风险值', dataIndex: 'dValue' },
  { title: '风险等级', dataIndex: 'riskLevel' },
];

export const levelColumns = [
  {
    title: '风险值',
    dataIndex: 'dValue',
    key: 'dValue',
    align: 'center',
  },
  {
    title: '风险等级',
    dataIndex: 'riskLevel',
    key: 'riskLevel',
    width: 300,
    align: 'center',
  },
  {
    title: '风险定级',
    dataIndex: 'classificationLabel',
    key: 'classificationLabel',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'description',
    key: 'description',
    align: 'center',
  },
];

export const serviceWrapper = (fieldName: string) => {
  return () => getLevelFieldsOptions({ fieldName });
};

export const optionsWrapper = (fieldName: string) => {
  return () => fetchSelectOptions(serviceWrapper(fieldName), 'description', 'matchValue');
};
