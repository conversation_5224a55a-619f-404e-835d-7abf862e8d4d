import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm, { fileNames } from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';
import { deleteReportInfo, getReportInfo, saveReport } from '@/services/hiddenDanger/dangerReport';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { Modal } from 'antd';
import { CorrectContainer } from './components/CorrectForm';

const HiddenDangerReport: React.FC = () => {
  const [columns, setColumns] = useState([...tableColumns]);
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [correctModalVisible, setCorrectModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    detailHandler,
    selectedRowsDel,
  } = useCommonFormPage({ HomeTitle });

  const { optionsMap } = useGeneralizedConfig(setTableFilters, setColumns, {
    correctionStatus: () => dictConfigWrapper('correction_status'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteReportInfo);
  };

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getReportInfo);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getReportInfo);
  };

  const timeHandle = (params: any) => {
    const { searchTime, ...rest } = params;
    let startTime;
    let endTime;
    if (Array.isArray(searchTime) && searchTime.length === 2) {
      startTime = searchTime[0]?.format('YYYY-MM-DD');
      endTime = searchTime[1]?.format('YYYY-MM-DD');
    }
    return {
      ...rest,
      startTime,
      endTime,
    };
  };

  const openCorrectModal = (record: any) => {
    setCurrentRecord(record);
    setCorrectModalVisible(true);
  };

  const closeCorrectModal = () => {
    setCorrectModalVisible(false);
    setCurrentRecord(null);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => {
        const matchValue = record.correctionStatus;
        const statusMap = optionsMap.correctionStatus;
        const showValue = statusMap?.find?.(v => v.label === '已整改')?.value;
        return (
          <div>
            <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
            <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
            {showValue === matchValue && (
              <CommonLinkButton onClick={() => openCorrectModal(record)}>整改</CommonLinkButton>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveReport)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        normalFileNames: fileNames,
        fileStringFormat: true,
      }}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/hiddendanger/hiddenreport/reportInfoList`}
        filterItems={[...tableFilters]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scrollYDelta={48}
        scroll={{ x: 2200 }}
        searchParamsHandle={timeHandle}
      />
      <Modal
        title="整改"
        open={correctModalVisible}
        onCancel={closeCorrectModal}
        footer={null}
        width={1200}
      >
        {currentRecord && <CorrectContainer record={currentRecord} onClose={closeCorrectModal} />}
      </Modal>
    </CommonPage>
  );
};

export default HiddenDangerReport;
