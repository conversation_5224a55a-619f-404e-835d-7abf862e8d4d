import React from 'react';
import { FormInstance, Modal } from 'antd';
import dayjs from 'dayjs';
import { DynamicFormItem, RULES } from 'jishan-components';

const { textAreaLengthRules, phoneRules } = RULES;

const opinionArr: [string, string][] = [
  ['unit', '所在单位意见'],
  ['completed', '完工验收意见'],
  ['department', '安全部门意见'],
  ['unit', '审核部门意见'],
  ['auditDepartment', '审核部门意见'],
  ['openFire', '审批人 / 审批部门意见'],
  ['ticket', '动火前，岗位当班班长验票情况'],
  ['engineer', '工程部意见'],
  ['facility', '设备物资部意见'],
  ['safety', '安全部意见'],
  ['safetyDirector', '安全总监意见'],
  ['distribution', '配送电单位意见'],
  ['commander', '作业指挥意见'],
];

// 校验作业人及联系方式
function checkWorkNamePhone(workNamePhone: string): true | string {
  // 必须包含 -
  if (!workNamePhone.includes('-')) {
    return '作业人及联系方式格式错误，请输入“姓名-电话”，如：张三-13812345678';
  }
  const [name, phone] = workNamePhone.split('-');
  if (!name || !phone || !phoneRules.pattern.test(phone)) {
    return '作业人及联系方式格式错误，请输入“姓名-电话”，如：张三-13812345678';
  }
  return true;
}

/**
 * 校验 attBatchParams 的规范性
 * @param attBatchParams 安全措施数组
 * @returns {true | string[]} true 表示校验通过，string[] 表示每条未通过的提示信息
 */
export function checkAttBatchParams(attBatchParams: any[]): true | string[] {
  if (!Array.isArray(attBatchParams)) return true; // 非数组直接通过
  const errors: string[] = [];

  let emptyCheck = false;
  attBatchParams.forEach((item, idx) => {
    const content = item?.measureContent;
    if (content) emptyCheck = true;
    const confirmer = item?.confirmer;
    if (content && (!confirmer || confirmer === '')) {
      errors.push(`第${idx + 1}条安全措施必须有一个确认人签名，否则安全措施无效`);
    }
  });
  if (!emptyCheck) {
    errors.push('安全措施不能为空');
  }
  return errors.length === 0 ? true : errors;
}

export function checkBlindDrawingName(blindDrawingName: string) {
  const errors: string[] = [];
  if (!blindDrawingName) {
    errors.push('安全措施的编制人不能为空');
  }
  return errors.length === 0 ? true : errors;
}

// 校验意见签名
export const checkOpinionAndName = (
  arr: [string, string][],
  data: Record<string, any>,
): string[] | true => {
  const errors: string[] = [];
  arr.forEach(([key, label]) => {
    const opinionKey = `${key}Opinion`;
    const nameKey = `${key}Name`;
    if (data[opinionKey] && !data[nameKey]) {
      errors.push(`${label}必须进行签名，否则无效`);
    }
  });
  return errors.length > 0 ? errors : true;
};

export const ticketFormCheckProcess = (data: Record<string, any> | boolean) => {
  if (typeof data === 'boolean') return data;
  const { workNamePhone, attBatchParams, blindDrawingName } = data;
  const errors: string[] = [];
  // 校验作业人及联系方式
  const workNamePhoneCheck = checkWorkNamePhone(workNamePhone);
  if (workNamePhoneCheck !== true) {
    errors.push(workNamePhoneCheck as string);
  }

  // 校验安全措施
  const attBatchParamsCheck = checkAttBatchParams(attBatchParams);
  if (attBatchParamsCheck !== true) {
    errors.push(...(attBatchParamsCheck as string[]));
  }

  const drawingNameCheck = checkBlindDrawingName(blindDrawingName);
  if (drawingNameCheck !== true) {
    errors.push(...(drawingNameCheck as string[]));
  }

  // 新增意见与签名校验
  if (opinionArr && Array.isArray(opinionArr)) {
    const opinionCheck = checkOpinionAndName(opinionArr, data);
    if (opinionCheck !== true) {
      errors.push(...(opinionCheck as string[]));
    }
  }

  if (errors.length > 0) {
    Modal.error({
      title: '校验未通过',
      content: (
        <div>
          {errors.map((err, idx) => (
            <div key={idx}>{err}</div>
          ))}
        </div>
      ),
    });
    return false;
  }
  return data;
};

export const setTicketTime = (data: any) => {
  const newData = { ...data };
  if (newData.actualTime) {
    newData.actualStartTime = newData.actualTime[0];
    newData.actualEndTime = newData.actualTime[1];
    delete newData.actualTime;
  }

  return newData;
};

export const revertTicketTime = (data: any) => {
  const newData = { ...data };
  // 处理 actualStartTime, actualEndTime 合成 actualTime
  if (newData.actualStartTime && newData.actualEndTime) {
    newData.actualTime = [newData.actualStartTime, newData.actualEndTime];
  }

  return newData;
};

export const setSignTimeWrapper = (name: string) => {
  return (form: FormInstance<any> | undefined) => {
    if (form) {
      form.setFieldValue(name, dayjs().format('YYYY-MM-DD HH:mm:ss'));
    }
  };
};

type GetOpinionType = (label: string, suffix: string, otherParams?: any) => DynamicFormItem[];

export const getOpinionItems: GetOpinionType = (label, suffix, otherParams = {}) => {
  return [
    {
      quickItemParams: [label, `${suffix}Opinion`, 'Input', false],
      span: 16,
      labelCol: { span: 4 },
      rules: [textAreaLengthRules],
      ...otherParams,
    },
    {
      quickItemParams: ['', `${suffix}Name`, 'Sign', false],
      signInfo: {
        signCallback: setSignTimeWrapper(`${suffix}Time`),
      },
    },
    { type: 'hideItem', name: `${suffix}Time` },
  ];
};
