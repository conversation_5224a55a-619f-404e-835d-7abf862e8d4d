import React from 'react';
import {
  DynamicForm,
  DynamicFormItem,
  buildRules,
  RULES,
  JSCUtils,
  FormOpenType,
} from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';

const { nameLengthRules, codeLengthRules, codeRules, textAreaLengthRules } = RULES;
const { setOption } = JSCUtils;

const formItems: DynamicFormItem[] = [
  {
    label: '证书名称',
    name: 'certificateName',
    rules: buildRules('请输入证书名称', true, nameLengthRules),
  },
  {
    label: '编号',
    name: 'certificateNo',
    rules: buildRules('请输入编号', true, codeRules, codeLengthRules),
  },
  {
    label: '单位名称',
    name: 'enterpriseName',
    type: 'ModalSelect',
    rules: buildRules('请选择单位名称', true, nameLengthRules),
    modalSelectInfo: companyModalSelectParams,
  },
  {
    label: '主要负责人',
    name: 'principal',
    rules: buildRules('请输入主要负责人', true, nameLengthRules),
  },
  {
    label: '单位地址',
    name: 'registeredAddress',
    type: 'TextArea',
    rules: buildRules('请输入单位地址', false, textAreaLengthRules),
  },
  {
    label: '经济类型',
    name: 'companyType',
    type: 'Select',
    rules: buildRules('请选择经济类型', false),
    options: [
      setOption('中央企业'),
      setOption('地方国有企业'),
      setOption('有限责任公司'),
      setOption('股份有限公司'),
      setOption('个人独资企业'),
      setOption('外商投资企业'),
    ],
  },
  {
    label: '许可范围',
    name: 'businessScope',
    rules: buildRules('请输入许可范围', true),
  },
  {
    label: '有效期起',
    name: 'validityStart',
    type: 'DatePicker',
    rules: buildRules('请选择有效期起', true),
  },
  {
    label: '有效期至',
    name: 'validityEnd',
    type: 'DatePicker',
    rules: buildRules('请选择有效期至', true),
  },
  {
    label: '发证机关',
    name: 'issuingAuthority',
    rules: buildRules('请输入发证机关', true, nameLengthRules),
  },
  {
    label: '发证时间',
    name: 'issuingAuthorityDate',
    type: 'DatePicker',
    rules: buildRules('请选择发证时间', true),
  },
  {
    label: '附件',
    name: 'attachmentUrl',
    type: 'FileUpload',
    rules: buildRules('请上传附件', true),
    uploadFileInfo: {
      tip: '附件大小不允许超过10M',
      maxFileSize: 10,
    },
  },
];

export const fileNames = ['attachmentUrl'];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
