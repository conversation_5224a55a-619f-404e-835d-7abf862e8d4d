import React from 'react';
import { Descriptions } from 'antd';

interface StatisticsDescriptionProps {
  total: number;
  valid: number;
  expired: number;
}

const StatisticsDescription: React.FC<StatisticsDescriptionProps> = ({ total, valid, expired }) => {
  return (
    <div style={{ width: '600px', marginBottom: 10 }}>
      <Descriptions column={3}>
        <Descriptions.Item label="资格证书数量">{total}</Descriptions.Item>
        <Descriptions.Item label="有效期内证书数量">{valid}</Descriptions.Item>
        <Descriptions.Item label="过期证书数量">
          <span style={{ color: '#ff4d4f' }}>{expired}</span>
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default StatisticsDescription;
