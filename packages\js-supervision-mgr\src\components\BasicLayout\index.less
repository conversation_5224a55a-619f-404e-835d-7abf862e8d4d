.basicLayoutContainer {
  position: relative;
  width: 100%;
  height: calc(100vh - 60px);

  .basicLayoutBody {
    position: relative;
    width: 100%;
    height: calc(100% - 62px);
  }

  .tabsName {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 24px 8px 18px;

    :global {
      .anticon-close {
        position: absolute;
        right: 5px;
      }

      .anticon {
        margin-right: 0;
      }
    }
  }

  :global {
    .ant-tabs {
      height: 100%;
      overflow: hidden;
    }

    .ant-tabs-content {
      height: calc(100vh - 99px);
      overflow-y: auto;
    }

    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
      padding: 0;
    }

    .ant-tabs-tab-btn {
      transition: all 0s;
    }
  }
}

.contextMenu {
  position: absolute;
  z-index: 999;
  margin: 0;
  padding: 4px 0;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;

  .contextMenuItem {
    display: flex;
    align-items: center;
    padding: 5px 12px;
    color: #000000d9;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

.custom-tabs {
  z-index: 1;
  margin: 0 !important;
  padding: 5px 6px 4px 6px;
  height: 40px;
  background: #ffffff;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  :global {
    .ant-tabs-tab {
      background: #f0f2f5 !important;
      border-radius: 4px 4px 4px 4px !important;
      .ant-tabs-tab-btn {
        color: #666666;
        font-weight: 400;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      }
    }
    .ant-tabs-tab-active {
      background: #3167d5 !important;
      .ant-tabs-tab-btn {
        color: white !important;
      }
    }
    .ant-dropdown-trigger{
      padding: 5px 24px 5px 18px;
    }
  }
}

.message-tips {
  position: fixed;
  right: 12px;
  bottom: 12px;
  z-index: 99;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: auto;
}

.noTabs {
  :global {
    .ant-tabs-nav {
      display: none;
    }
  }
}
