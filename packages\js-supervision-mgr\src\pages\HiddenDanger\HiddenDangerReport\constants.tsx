import React from 'react';
import { CustomButton, FixedWidthRangePicker, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '企业自查隐患上报';

export const filterItems = [
  {
    name: 'searchTime',
    label: '检查日期',
    component: <FixedWidthRangePicker style={{ width: '100%' }} />,
    span: 6,
  },
  {
    name: 'registrant',
    label: '检查人员',
    component: <SearchInput placeholder="请输入检查人员" />,
    span: 6,
  },
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入企业名称" />,
    span: 6,
  },
  {
    name: 'correctionStatus',
    label: '整改状态',
    component: <SearchInput placeholder="请输入整改状态" />,
    span: 6,
  },
  {
    name: 'dangerName',
    label: '隐患名称',
    component: <SearchInput placeholder="请输入隐患名称" />,
    span: 6,
  },
  {
    name: 'dangerLevel',
    label: '隐患级别',
    component: <SearchInput placeholder="请输入隐患级别" />,
    span: 6,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true, width: 200 },
  { title: '排查时间', dataIndex: 'inspectionTime', width: 160 },
  { title: '登记人', dataIndex: 'registrant', width: 120 },
  { title: '隐患名称', dataIndex: 'dangerName', width: 200 },
  { title: '隐患级别', dataIndex: 'dangerLevel', width: 120 },
  { title: '隐患描述', dataIndex: 'dangerDescription', width: 300 },
  { title: '整改措施', dataIndex: 'correctiveAction', width: 300 },
  { title: '整改时限', dataIndex: 'correctionDeadline', width: 120 },
  { title: '整改效果', dataIndex: 'correctionEffect', width: 200 },
  { title: '整改责任人', dataIndex: 'responsiblePerson', width: 120 },
  { title: '整改状态', dataIndex: 'correctionStatus', width: 120 },
];
