import React, { useEffect } from 'react';
import { DatePicker } from 'antd';
import { DynamicFormItem } from '../interface';
import dayjs from 'dayjs';

interface DFDatePickerProps {
  item: DynamicFormItem;
  value?: any; // 接收 Form.Item 灌注下来的 value
  onChange?: (value: any) => void; // 接收 Form.Item 灌注下来的 onChange
}

const DFRangePicker: React.FC<DFDatePickerProps> = ({ item, value, onChange }) => {
  const { showTime, initValue, placeholder } = item.rangePickerInfo || {};

  useEffect(() => {
    if (initValue) onChange?.(initValue);
  }, []);

  const handle = (v: any) => {
    const timeOne = dayjs(v[0]).format(showTime?.format || 'YYYY-MM-DD');
    const timeTwo = dayjs(v[1]).format(showTime?.format || 'YYYY-MM-DD');
    onChange?.([timeOne, timeTwo]);
  };

  return (
    <DatePicker.RangePicker
      showTime={showTime}
      placeholder={placeholder}
      style={{ width: '100%' }}
      value={value ? [dayjs(value[0]), dayjs(value[1])] : undefined}
      onChange={handle}
    />
  );
};

export default DFRangePicker;
