import React from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import { tableColumns, HomeTitle, filterItems, customButtons, exportUtl } from './constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

import { deleteGrading } from '@/services/riskLevelMgr/gradingList';

const GradingList: React.FC = () => {
  const {
    updateTrigger,
    formTitle,
    setFormValues,
    onRowSelect,
    turnToListPage,
    selectedRowsDel,
    deleteHandler,
    setExportEvent,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = () => {
    selectedRowsDel(deleteGrading);
  };

  customButtons[1].onClick = setExportEvent(exportUtl, HomeTitle);

  const handleDeleteOne = (record: any) => {
    deleteHandler([record.id], deleteGrading);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton onClick={() => handleDeleteOne(record)}>删除</CommonLinkButton>
          </div>
        );
      },
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      formVisible={false}
      formComp={<div />}
      onClose={turnToListPage}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/riskLevel/controlList/gradingList`}
        getFormValues={setFormValues}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowKey="rowId"
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default GradingList;
