import React, { useEffect, useState } from 'react';
import { Form, Button, Row, Spin } from 'antd';
import { Rule } from 'antd/lib/form';
import {
  getFormNames,
  getRecordInitValue,
  processImageFields,
  processCascadeFields,
  restoreCascadeFields,
  formatFormBatchImages,
  parseFormBatchImages,
  convertImageStringToArray,
} from '../../utils/form';
import './index.less';

export interface FormItem {
  label: string;
  prop: string;
  rules?: Rule[];
  disabled?: boolean;
  type?: string;
  span?: number;
  component?: React.ReactNode;
}

export type FormOpenType = 'add' | 'edit' | 'view';

type Handle = (formData?: any) => boolean | Promise<boolean | void>;

export interface FormFieldOptions {
  imageFieldFormatControl?: boolean; // 控制图片字段格式处理
  imageFieldNames?: string[]; // 指定图片字段名
  normalFileNames?: string[]; // 指定普通文件字段名
  batchImgName?: string; // 将图片字段放入一个字段中的具体字段
  cascadeFieldsMap?: Record<string, string[]>; // 级联下拉框存值的映射关系
  imgStringFormat?: boolean; // 是否将图片属性从数组转换成字符串（仅数组长度为 1 时生效）
  fileStringFormat?: boolean; // 是否将文件属性从数组转换成字符串（仅数组长度为 1 时生效）
  /**
   * 是否有动态加载的表单项。若有则必传 true，因为动态的表单项
   * 尤其是 DynamicFormList 加载的，一定会比 form 晚渲染，
   * 此时仅用 form 自带的方法是无法获取全部有效表单属性，需要走单独的逻辑
   */
  hasDynamicFormFields?: boolean;
}

export interface FormContainerProps {
  visible: boolean;
  onClose: () => void;
  openType?: FormOpenType;
  showDefaultButtons?: boolean;
  customButtons?: React.ReactNode;
  validateOnSubmit?: boolean;
  onSubmit?: Handle;
  onCancel?: Handle;
  formComp?: any;
  initialValues?: any;
  containerClass?: string;
  formClass?: string;
  formLoading?: boolean;
  formFieldOptions?: FormFieldOptions; // 统一表单项参数对象
  gutter?: [number, number];
  setFormInstance?: (form: any) => void;
}

let formInitValue: any = {};

const FormContainer: React.FC<FormContainerProps> = ({
  visible,
  onClose,
  openType = 'add',
  showDefaultButtons = true,
  customButtons,
  validateOnSubmit = true,
  onSubmit,
  onCancel,
  formComp,
  initialValues,
  containerClass,
  formClass,
  formLoading = false,
  formFieldOptions = {},
  gutter,
  setFormInstance = () => {},
}) => {
  const {
    imageFieldFormatControl = true,
    imageFieldNames = [],
    normalFileNames = [],
    cascadeFieldsMap = {},
    batchImgName = '',
    imgStringFormat = false,
    fileStringFormat = false,
    hasDynamicFormFields = false,
  } = formFieldOptions;

  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  formInitValue = initialValues;

  useEffect(() => {
    if (openType === 'edit' || openType === 'view') {
      let values = initialValues;
      if (initialValues) {
        const formNames = getFormNames(form, imageFieldNames, batchImgName, cascadeFieldsMap);
        values = getRecordInitValue(initialValues, formNames, hasDynamicFormFields);

        // 处理 imgStringFormat 情形下对应字段的格式化
        if (imgStringFormat) values = convertImageStringToArray(values, imageFieldNames);
        if (fileStringFormat) values = convertImageStringToArray(values, normalFileNames);
        // 处理级联下拉框字段
        values = restoreCascadeFields(values, cascadeFieldsMap);
        if (batchImgName) {
          // 将图片从一个字段中恢复出来
          values = parseFormBatchImages(
            imageFieldNames.map(item => ({ [item]: item })),
            values,
            batchImgName,
          );
        }
      }
      form.setFieldsValue(values);
    } else {
      form.resetFields();
      formInitValue = {};
    }
    setFormInstance(form);
  }, [openType, initialValues, form]);

  const judgeCloseByHandle = async (handle?: (values: any) => Promise<any> | any) => {
    if (handle) {
      const rawValues = form.getFieldsValue();
      // 处理级联下拉框字段
      const cascadeProcessedValues = processCascadeFields(rawValues, cascadeFieldsMap);
      // 处理普通文件字段
      const normalFileProcessedValues = processImageFields(
        cascadeProcessedValues,
        normalFileNames,
        true,
        fileStringFormat,
      );
      // 处理图片字段
      const processedValues = processImageFields(
        normalFileProcessedValues,
        imageFieldNames,
        imageFieldFormatControl,
        imgStringFormat,
      );

      // 如果需要将图片字段放入到一个字段中
      let batchedValues = processedValues;
      if (batchImgName) {
        batchedValues = formatFormBatchImages(
          imageFieldNames.map(item => ({ [item]: item })),
          processedValues,
          batchImgName,
        );
      }

      try {
        const result = await handle(batchedValues);
        if (result !== false) {
          onClose();
          form.resetFields();
        }
      } catch (error) {
        setSubmitting(false);
      }
    } else {
      onClose();
      form.resetFields();
    }
  };

  const handleSubmit = async () => {
    if (submitting) return;
    setSubmitting(true);
    if (validateOnSubmit) {
      try {
        await form.validateFields();
      } catch (error) {
        setSubmitting(false);
        return;
      }
    }
    await judgeCloseByHandle(onSubmit);
    setSubmitting(false);
  };

  const handleCancel = () => {
    judgeCloseByHandle(onCancel);
  };

  // 值一定会传给子组件，若子组件没接收到，自行检查与子组件之间是否存在中间组件
  const enhancedFormComp = formComp && React.cloneElement(formComp, { form, formInitValue });

  return (
    <div className={`${containerClass || ''} ${visible && 'jishan-comp-form-container'}`}>
      {visible && (
        <div className="common-page-body module-detail">
          <div className={`form ${formClass || ''}`}>
            <Spin className="spin-container" spinning={formLoading}>
              <div className={`form ${formClass || ''}`}>
                <Form form={form} labelCol={{ span: 4 }} disabled={openType === 'view'}>
                  <Row gutter={gutter ?? [16, 0]}>{enhancedFormComp}</Row>
                </Form>
              </div>
            </Spin>
          </div>
          <div className="submit-wrap" style={{ textAlign: 'right' }}>
            {showDefaultButtons && (
              <>
                {openType !== 'view' ? (
                  <Button
                    className="btn"
                    loading={submitting}
                    disabled={submitting}
                    type="primary"
                    onClick={handleSubmit}
                  >
                    提交
                  </Button>
                ) : (
                  <Button className="btn" type="primary" onClick={onClose}>
                    确定
                  </Button>
                )}
                {customButtons}
                <Button className="btn" onClick={handleCancel}>
                  取消
                </Button>
              </>
            )}
            {!showDefaultButtons && customButtons}
          </div>
        </div>
      )}
    </div>
  );
};

export default FormContainer;
