import { request } from '@/utils/net';

export async function savePerson(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/personMsg/savePerson`, params);
}

export async function getPersonDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/personMsg/personDetail`, {
    id,
  });
}

export async function deletePerson(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/personMsg/removePerson`, params);
}

export async function getPersonList(params: Record<string, any>) {
  return request.get(`${SPPREFIX}/basicinformation/personMsg/personList`, {
    pageNo: 1,
    pageSize: 10000,
    ...params,
  });
}
