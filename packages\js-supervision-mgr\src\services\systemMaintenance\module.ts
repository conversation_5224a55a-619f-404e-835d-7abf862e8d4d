import { request } from 'umi';

// 样式列表
export async function listModuleInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/styleConf/styleConfList`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

// 栏目列表
export async function getColumnList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/columnMgr/columnList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
