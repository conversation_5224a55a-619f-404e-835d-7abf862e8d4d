import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '生产工艺信息';

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入企业名称" style={{ width: '100%' }} />,
  },
  {
    name: 'processName',
    label: '重点监管危化工工艺名称',
    component: <SearchInput placeholder="请输入重点监管危化工工艺名称" style={{ width: '100%' }} />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '企业名称', dataIndex: 'enterpriseName', ellipsis: true, width: 200 },
  { title: '重点监管危化工工艺名称', dataIndex: 'processName', ellipsis: true, width: 220 },
  { title: '产品', dataIndex: 'product', ellipsis: true, width: 150 },
  { title: '副产品', dataIndex: 'byproduct', ellipsis: true, width: 200 },
  { title: '工艺位置', dataIndex: 'processLocation', ellipsis: true, width: 350 },
];
