.internal-management {
  padding: 20px 0;
  
  // 表格样式优化
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
      text-align: center;
    }
    
    .ant-table-tbody > tr > td {
      text-align: center;
      vertical-align: middle;
      
      // 文件链接样式
      .ant-btn-link {
        padding: 0;
        height: auto;
        color: #1890ff;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
    
    // 序号列样式
    .ant-table-tbody > tr > td:first-child {
      font-weight: 500;
      color: #666;
    }
  }
  
  // 分页样式
  .pagination-wrap {
    margin-top: 16px;
    text-align: right;
    
    .ant-pagination {
      .ant-pagination-total-text {
        color: #666;
      }
    }
  }
  
  // 搜索表单样式
  .ant-form {
    margin-bottom: 16px;
    
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .ant-btn {
      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        
        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .internal-management {
    padding: 16px 0;
    
    .ant-table {
      font-size: 12px;
      
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
    }
    
    .ant-form {
      .ant-form-item {
        margin-bottom: 12px;
      }
    }
  }
}

@media (max-width: 576px) {
  .internal-management {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 6px 2px;
        font-size: 11px;
      }
      
      // 在小屏幕上隐藏部分列
      .ant-table-thead > tr > th:nth-child(4),
      .ant-table-tbody > tr > td:nth-child(4) {
        display: none;
      }
    }
  }
}
