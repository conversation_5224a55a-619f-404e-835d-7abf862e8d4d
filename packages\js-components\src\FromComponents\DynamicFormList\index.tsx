import React, { useEffect, useState, ComponentType } from 'react';
import { FormInstance, Button, Row, Modal } from 'antd';
import { getCompName } from '../../utils/commonFunction';
import { PlusOutlined, CopyOutlined } from '@ant-design/icons';
import type { FormOpenType } from '../FormContainer';
import './index.less';

interface DynamicFormListProps<T = any> {
  form?: FormInstance<T>;
  // 弹窗类型
  openType?: FormOpenType;
  // 传入的表单组件，必须接收 cardIndex、form、type 等 props
  FormComponent: ComponentType<any>;
  // 复制按钮是否显示，默认隐藏
  showCopyButton?: boolean;
  // 新增按钮是否显示，默认显示
  showAddButton?: boolean;
  // 传递给表单的额外props
  formComponentProps?: Record<string, any>;
  // 重置触发参数，注意绑定 state
  resetKey?: number;
  initValues?: Record<string, any>;
  cardCount?: number;
}

const DynamicFormList: React.FC<DynamicFormListProps> = ({
  form,
  openType,
  resetKey,
  FormComponent,
  initValues = {},
  cardCount,
  showCopyButton = false,
  showAddButton = true,
  formComponentProps = {},
}) => {
  const [formCards, setFormCards] = useState<number[]>([0]);
  const [cardIndexCounter, setCardIndexCounter] = useState<number>(1);
  const [editDisabled, setEditDisabled] = useState(false);

  useEffect(() => {
    // 每次 resetKey 变化时重置 formCards 和 cardIndexCounter
    setFormCards([0]);
    setCardIndexCounter(1);
  }, [resetKey]);

  useEffect(() => {
    if (openType !== 'add' && cardCount && cardCount > 0) {
      setCardIndexCounter(cardCount || 1);
      const car = Array.from({ length: cardCount }, (_, i) => i);
      setFormCards(car);
      if (openType === 'view') setEditDisabled(true);
    } else setEditDisabled(false);
  }, [initValues]);

  // 复制卡片
  const handleCopy = (cardIndex: number) => {
    const newCardIndex = cardIndexCounter;
    setCardIndexCounter(cardIndexCounter + 1);
    const currentValues = form?.getFieldsValue() || {};
    const newValues: Record<string, any> = {};
    Object.keys(currentValues).forEach(key => {
      if (key.endsWith(`_${cardIndex}`)) {
        const newKey = key.replace(`_${cardIndex}`, `_${newCardIndex}`);
        newValues[newKey] = currentValues[key];
      }
    });
    form?.setFieldsValue(newValues);
    setFormCards([...formCards, newCardIndex]);
  };

  // 新增空卡片
  const handleAdd = () => {
    setFormCards([...formCards, cardIndexCounter]);
    setCardIndexCounter(cardIndexCounter + 1);
  };

  // 删除卡片
  const handleDelete = (index: number) => {
    return Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个表单吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        setFormCards(formCards.filter((_, i) => i !== index));
      },
    });
  };

  // 复制按钮渲染逻辑
  const renderCopyBtn = (cardIndex: number) => {
    if (openType === 'add' && showCopyButton && !editDisabled) {
      return (
        <Button icon={<CopyOutlined />} onClick={() => handleCopy(cardIndex)} block>
          复制
        </Button>
      );
    }
    return '';
  };

  const renderAddBtn = (index: number) => {
    if (!editDisabled && openType === 'add' && showAddButton && index === formCards.length - 1) {
      return (
        <Button
          type="dashed"
          className={getCompName('add-btn')}
          icon={<PlusOutlined />}
          onClick={handleAdd}
          block
        >
          新增
        </Button>
      );
    }
    return '';
  };

  return (
    <>
      {formCards.map((cardIndex, index) => (
        <div key={index} className={getCompName('dynamic-form')}>
          <div className={getCompName('dynamic-form-list')}>
            {index !== 0 && !editDisabled && (
              <Button
                danger
                type="primary"
                className="delete-btn"
                onClick={() => handleDelete(index)}
              >
                删除
              </Button>
            )}
            <Row gutter={[16, 0]}>
              <FormComponent cardIndex={cardIndex} form={form} {...formComponentProps} />
            </Row>
            <div className="copy-btn">{renderCopyBtn(cardIndex)}</div>
          </div>
          {renderAddBtn(index)}
        </div>
      ))}
    </>
  );
};

export default DynamicFormList;
