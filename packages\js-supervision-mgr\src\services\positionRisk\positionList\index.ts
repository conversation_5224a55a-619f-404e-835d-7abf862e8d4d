import { request } from '@/utils/net';

export async function savePosition(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/riskCheck/maintain/savePosition`, params);
}

export async function getPositionDetail(id: number) {
  return request.get(`${SPPREFIX}/riskCheck/maintain/positionDetail`, {
    id,
  });
}

export async function deletePositionInfo(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/riskCheck/maintain/removePosition`, params);
}

export async function getPositionList(params: Record<string, any>) {
  return request.get(`${SPPREFIX}/riskCheck/maintain/positionList`, {
    pageNo: 1,
    pageSize: 10000,
    ...params,
  });
}
