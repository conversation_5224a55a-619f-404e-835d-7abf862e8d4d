import React, { useRef, useState, useEffect } from 'react';
import { Button, FormInstance, Modal, Radio, message } from 'antd';
import SignatureCanvas from 'react-signature-canvas';
import { PREFIX_SERVER } from '../../../global';
import { getCompName } from '../../../utils/commonFunction';
import './components.less';
import { FormOpenType } from '../../FormContainer';
import { DynamicFormItem } from '../interface';

export interface SignaturePadProps {
  value?: string; // 预览地址
  onChange?: (value: string) => void;
  openType?: FormOpenType; // 可选，表示表单的操作类型
  type?: 'link' | 'text' | 'dashed' | 'primary' | 'default' | undefined;
  showReSign?: boolean;
  showThumbnail?: boolean;
  item?: DynamicFormItem;
  form?: FormInstance<any> | undefined;
}

const COLOR_OPTIONS = [
  { label: '黑色', value: 'black' },
  { label: '红色', value: 'red' },
  { label: '蓝色', value: 'blue' },
];

const DFSignaturePad: React.FC<SignaturePadProps> = ({
  value,
  onChange,
  openType,
  type,
  showReSign = true,
  item,
  form,
  showThumbnail = true,
}) => {
  const { signInfo } = item || {};

  const [visible, setVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [signature, setSignature] = useState<string>(value || '');
  const [uploading, setUploading] = useState(false);
  const [penColor, setPenColor] = useState<string>('black');
  const sigPadRef = useRef<SignatureCanvas>(null);

  // 同步外部 value
  useEffect(() => {
    value && setSignature(value || '');
  }, [value]);

  // 清除签名
  const handleClear = () => {
    sigPadRef.current?.clear();
  };

  // base64 转 File
  function dataURLtoFile(dataurl: string, filename: string) {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n > 0) {
      n -= 1;
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }

  // 上传签名图片
  const uploadSignature = async (file: File) => {
    const formData = new FormData();
    formData.append('uploadFile', file);
    setUploading(true);
    try {
      const res = await fetch(`${PREFIX_SERVER}/api/simple-uploadFile?bucket=png`, {
        method: 'POST',
        body: formData,
        headers: {
          access_token: localStorage.getItem('jishan_token') || '',
        },
      });
      const { code, data } = await res.json();
      if (code === 200) {
        const { downloadUrl } = data;
        const { signCallback } = signInfo || {};
        setSignature(downloadUrl);
        onChange?.(downloadUrl);
        signCallback?.(form, downloadUrl);
        setVisible(false);
        message.success('签名保存成功');
      } else {
        message.error('上传失败');
      }
    } catch (e) {
      message.error('上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 保存签名
  const handleSave = async () => {
    if (sigPadRef.current && !sigPadRef.current.isEmpty()) {
      const dataUrl = sigPadRef.current.getTrimmedCanvas().toDataURL('image/png');
      const file = dataURLtoFile(dataUrl, 'signature.png');
      await uploadSignature(file);
    } else {
      message.warning('请先签名');
    }
  };

  // 重新签名
  const handleReSign = () => {
    setSignature('');
    onChange?.('');
  };

  // 预览签名
  const handlePreview = () => {
    setPreviewVisible(true);
  };

  // 颜色切换
  const handleColorChange = (e: any) => {
    setPenColor(e.target.value);
    // 切换颜色时不清除画布
  };

  // Modal 取消按钮替换为清除
  const handleModalCancel = () => {
    handleClear();
  };

  const renderBtn = () => {
    if (openType === 'view') return '';
    if (!signature) {
      return (
        <Button type={type} onClick={() => setVisible(true)}>
          签名
        </Button>
      );
    }
    return (
      <div className={getCompName('sign-container')}>
        {showThumbnail && (
          <img src={`${PREFIX_SERVER}/${signature}`} alt="签名缩略图" onClick={handlePreview} />
        )}
        {showReSign && (
          <Button size="small" onClick={handleReSign}>
            重新签名
          </Button>
        )}
      </div>
    );
  };

  return (
    <div>
      {renderBtn()}
      <Modal
        title="签名"
        open={visible}
        width={750}
        onCancel={() => setVisible(false)}
        destroyOnHidden
        confirmLoading={uploading}
        footer={
          <div className={getCompName('sign-modal-footer')}>
            <Radio.Group
              options={COLOR_OPTIONS}
              onChange={handleColorChange}
              value={penColor}
              buttonStyle="solid"
            />
            <div>
              <Button onClick={handleModalCancel} loading={uploading}>
                清空
              </Button>
              <Button
                type="primary"
                onClick={handleSave}
                loading={uploading}
                style={{ marginLeft: 8 }}
              >
                保存
              </Button>
            </div>
          </div>
        }
      >
        <SignatureCanvas
          ref={sigPadRef}
          penColor={penColor}
          canvasProps={{ width: 700, height: 300, style: { border: '1px solid #eee' } }}
        />
      </Modal>
      <Modal
        open={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        title="签名预览"
      >
        <img
          className={getCompName('preview-img')}
          alt="签名预览"
          src={`${PREFIX_SERVER}/${signature}`}
        />
      </Modal>
    </div>
  );
};

export default DFSignaturePad;
