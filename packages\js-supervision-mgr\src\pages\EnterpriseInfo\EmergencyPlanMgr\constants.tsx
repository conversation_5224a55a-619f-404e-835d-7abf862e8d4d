import React from 'react';
import { CustomButton, FixedWidthRangePicker, SearchInput, JSCUtils } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

const { getTemplateDownloadUrl } = JSCUtils;

export const HomeTitle = '应急预案备案管理';

export const importUrl = `${SPPREFIX}/basicinformation/contingencyPlan/importPlan`;

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入企业名称" style={{ width: '100%' }} />,
  },
  {
    name: 'filingDate',
    label: '备案期限',
    component: <FixedWidthRangePicker placeholder={['备案期限起', '备案期限止']} />,
  },
];

const urlArray: [string, string] = [
  `${HomeTitle}.xlsx`,
  '20250718/644aa972-d3e8-498e-8166-8c7ead42294f.xlsx',
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '导入', ...baseButtonStyle },
  {
    text: '模板下载',
    ...baseButtonStyle,
    onClick: () => window.open(getTemplateDownloadUrl(...urlArray), '_blank'),
  },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '企业名称', dataIndex: 'enterpriseName', width: 200 },
  { title: '注册地址', dataIndex: 'registrationAddress', width: 220 },
  { title: '主要负责人', dataIndex: 'chiefPerson', width: 140 },
  { title: '联系方式', dataIndex: 'contactPhone', width: 150 },
  { title: '预案编号', dataIndex: 'planNumber', width: 160 },
  { title: '备案日期', dataIndex: 'filingDate', width: 130 },
  { title: '备案有效期', dataIndex: 'validityDate', width: 130 },
];
