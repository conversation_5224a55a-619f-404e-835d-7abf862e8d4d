import React, { useState } from 'react';
import { Form, Input, Col, FormInstance } from 'antd';
import DFIntegerInput from './components/DFIntegerInput';
import DFFloatInput from './components/DFFloatInput';
import DFImgUpload from './components/DFImgUpload';
import DFSelect from './components/DFSelect';
import { FormOpenType } from '../FormContainer';
import { DynamicFormItem, DynamicFormItemType, FormItemParams, WatchInfoType } from './interface';
import DFCascader from './components/DFCascader';
import DFFileUpload from './components/DFFileUpload';
import FormControlWrapper from './components/FormControlWrapper';
import HiddenFormItem from './components/HiddenFormItem';
import DFModalSelect from './components/DFModalSelect';
import DFRadio from './components/DFRadio';
import DFSignaturePad from './components/DFSignaturePad';
import DFDatePicker from './components/DFDatePicker';
import DFRangePicker from './components/DFRangePicker';
import DFSelectTree from './components/DFSelectTree';
import { getCompName, getRelatedValue } from '../../utils/commonFunction';
import './index.less';

interface DynamicFormProps {
  // items: 表单项的配置数组，每一项描述一个表单控件
  items: DynamicFormItem[];
  // nameSuffix: 可选，表单项 name 的后缀，用于区分同名表单项
  nameSuffix?: string | number;
  openType?: FormOpenType; // 可选，表示表单的操作类型
  form?: FormInstance<any> | undefined;
  formInitValue?: any;
}

interface FormItemExtraProps {
  formStoreHandle: (item: DynamicFormItem, value: any) => void;
}

const renderFormControl = (
  item: DynamicFormItem,
  openType: FormOpenType = 'view',
  extraProps: FormItemExtraProps,
  form: FormInstance<any> | undefined,
  formInitValue: any,
) => {
  if (item.component) {
    if (React.isValidElement(item.component)) {
      // @ts-ignore
      return React.cloneElement(item.component, { form, formInitValue });
    }
    return item.component;
  }

  const { formStoreHandle } = extraProps;

  const placeholder = item.placeholder || item.rules?.[0]?.message || '';

  const params: FormItemParams = {
    placeholder,
  };
  params.disabled = item.disabled;

  switch (item.type) {
    case 'Select':
      return (
        <DFSelect {...params} openType={openType} item={item} formStoreHandle={formStoreHandle} />
      );
    case 'Radio':
      return <DFRadio {...params} item={item} formStoreHandle={formStoreHandle} />;
    case 'IntegerInput':
      // 只允许输入整数
      return <DFIntegerInput {...params} />;
    case 'FloatInput':
      // 只允许输入浮点数
      return <DFFloatInput item={item} {...params} />;
    case 'TextArea':
      return <Input.TextArea {...params} />;
    case 'DatePicker':
      return <DFDatePicker {...params} item={item} />;
    case 'RangePicker':
      return <DFRangePicker {...params} item={item} />;
    case 'ModalSelect':
      return (
        <DFModalSelect {...params} form={form} item={item} formStoreHandle={formStoreHandle} />
      );
    case 'Sign':
      return <DFSignaturePad openType={openType} item={item} form={form} />;
    case 'ImgUpload':
      return <DFImgUpload item={item} openType={openType} />;
    case 'TreeSelect':
      return (
        <DFSelectTree
          {...params}
          openType={openType}
          item={item}
          formStoreHandle={formStoreHandle}
        />
      );
    case 'FileUpload':
      return <DFFileUpload item={item} openType={openType} />;
    case 'Cascader':
      return <DFCascader {...params} item={item} />;
    case 'Input':
    default:
      return <Input {...params} />;
  }
};

const DynamicForm: React.FC<DynamicFormProps> = ({
  items,
  nameSuffix,
  openType,
  form,
  formInitValue,
}) => {
  const [storeUpdate, setStoreUpdate] = useState(false);
  const [formStore, setFormStore] = useState<Record<string, any>>({});

  const getName = (baseName: string) =>
    nameSuffix !== undefined ? `${baseName}_${nameSuffix}` : baseName;

  const formStoreHandle = (item: DynamicFormItem, value: any) => {
    formStore[getName(item?.name || '')] = value;
    setFormStore(formStore);
    setStoreUpdate(!storeUpdate);
  };

  const formExtraProps = {
    formStoreHandle,
  };

  const getDefaultPlaceholder = (label: string, type: DynamicFormItemType, temp?: string) => {
    if (temp) return temp;
    if (['Select', 'DatePicker'].includes(type)) return `请选择${label}`;
    return `请输入${label}`;
  };

  const getItemShowCondition = (condition?: WatchInfoType) => {
    if (condition) {
      const { watchTarget, matchedValue } = condition;
      const targetValue = getRelatedValue(formStore, getName, watchTarget);
      if (targetValue === matchedValue) return true;
      return false;
    }
    return true;
  };

  return (
    <>
      {items.map((item, idx) => {
        let currentItem = item; // 新增变量，避免直接修改 item
        if (item.quickItemParams) {
          const {
            quickItemParams: [label, name, type, required, placeholder],
          } = item;
          const usedHolder = getDefaultPlaceholder(label, type, placeholder);
          currentItem = {
            ...item,
            label,
            name,
            type,
            placeholder: usedHolder,
            rules: [...(item.rules || []), { message: usedHolder, required }],
          };
        }
        const showItem = getItemShowCondition(item.showConditionInfo);
        if (!showItem) return '';
        // 空项或 name 为空字符串时，仅渲染占位 Col
        if (!currentItem.name || currentItem.type === 'blank') {
          return <Col span={currentItem.span || 8} key={idx} />;
        }
        if (currentItem.type === 'hideItem') {
          return (
            <HiddenFormItem
              key={idx}
              item={currentItem}
              getName={getName}
              formStore={formStore}
              storeUpdate={storeUpdate}
              form={form}
            />
          );
        }
        return (
          <Col span={currentItem.span || 8} key={idx}>
            <Form.Item
              className={currentItem.flexLabel ? getCompName('df-form-item') : ''}
              labelCol={currentItem.labelCol || { span: 8 }}
              label={currentItem.customLabel || currentItem.label}
              style={{ overflow: 'visible' }}
              name={getName(currentItem.name)}
              rules={currentItem.rules}
              initialValue={currentItem.initialValue}
            >
              <FormControlWrapper openType={openType} extraContent={currentItem.extraContent}>
                {renderFormControl(currentItem, openType, formExtraProps, form, formInitValue)}
              </FormControlWrapper>
            </Form.Item>
          </Col>
        );
      })}
    </>
  );
};

export default DynamicForm;
