import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  getLocationItem,
  OpinionItem,
} from '../constants';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { DynamicFormItem, SearchInput, RULES } from 'jishan-components';
import SafetyMeasuresList from '../components/Measures';
import { getOpinionItems } from '../utils';

const { nameLengthRules } = RULES;

const HomeTitle = '高处作业';

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  { title: '作业地点', dataIndex: 'workLocation', width: 220 },
  { title: '高处作业级别', dataIndex: 'highWorkLevel', width: 220 },
);

const levelItem = {
  name: 'highWorkType',
  label: '高处作业级别',
  component: <SearchInput placeholder="请输入高处作业级别" />,
};

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.applyTime,
  { quickItemParams: ['作业地点', 'workLocation', 'Input', true], rules: [nameLengthRules] },
  FrequencyItems.workContent,
  { quickItemParams: ['作业高度(m)', 'workHeight', 'FloatInput', true], decimalPlaces: 1 },
  {
    quickItemParams: ['高处作业级别', 'highWorkLevel', 'Select', true],
    optionsServices: () => dictConfigWrapper('high_work_type'),
    selectInfo: {
      onOptionsLoaded: options => options.find(v => v.label === 'IV级')?.value,
    },
  },
  FrequencyItems.workUnit,
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  FrequencyItems.supervisor,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  FrequencyItems.actualTime,
  FrequencyItems.workPlace,
  FrequencyItems.workImplementPlace,
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  ...OpinionItem.jobOpinion,
  ...OpinionItem.unitOpinion,
  ...OpinionItem.auditDepartmentOpinion,
  ...getOpinionItems('审批部门意见', 'openFire'),
  ...OpinionItem.completedOpinion,
  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'blindDrawingName' },
];

const HighPlaceWork: React.FC = () => {
  const configMap = {
    highWorkLevel: () => dictConfigWrapper('high_work_type'),
  };

  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[getLocationItem('作业地点'), applyTimeItem, levelItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      configMap={configMap}
      keyName={HomeTitle}
      formItems={formItems}
      x={1400}
    />
  );
};

export default HighPlaceWork;
