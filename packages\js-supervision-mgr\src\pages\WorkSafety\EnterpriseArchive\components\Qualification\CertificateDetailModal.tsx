import React from 'react';
import { Modal, Descriptions, Row, Col } from 'antd';

interface CertificateDetailModalProps {
  visible: boolean;
  onClose: () => void;
  data: any;
}

const CertificateDetailModal: React.FC<CertificateDetailModalProps> = ({
  visible,
  onClose,
  data,
}) => {
  if (!data) return null;

  // 判断证书是否过期
  const isExpired = data.status === 'expired';

  // 根据证书类型和状态渲染不同的详情信息
  const renderContent = () => {
    if (!isExpired) {
      // 有效状态的危险化学品经营许可证
      return (
        <Descriptions column={2} bordered size="small">
          <Descriptions.Item label="证书编号">(晋) WH安许证 [2024] 235B2Y1号</Descriptions.Item>
          <Descriptions.Item label="证书名称">危险化学品经营许可证</Descriptions.Item>
          <Descriptions.Item label="发证机关">山西省应急管理厅</Descriptions.Item>
          <Descriptions.Item label="发证日期">2024-11-4</Descriptions.Item>
          <Descriptions.Item label="企业名称">稷山县永东化工股份有限公司</Descriptions.Item>
          <Descriptions.Item label="企业法定代表人">刘东杰</Descriptions.Item>
          <Descriptions.Item label="企业住所">
            山西省运城市稷山县稷山经济技术开发区振西大街东
          </Descriptions.Item>
          <Descriptions.Item label="经营方式">批发</Descriptions.Item>
          <Descriptions.Item label="有效期自">2024-11-4</Descriptions.Item>
          <Descriptions.Item label="有效期至">2027-11-3</Descriptions.Item>
          <Descriptions.Item label="许可范围" span={2}>
            氯氢酸、氯氧化钠、三乙醇胺
          </Descriptions.Item>
          <Descriptions.Item label="附件" span={2}>
            永东危化品经营许可证.jpg
          </Descriptions.Item>
        </Descriptions>
      );
    } else if (isExpired) {
      // 过期状态的危险化学品企业安全生产许可证
      return (
        <Descriptions column={2} bordered size="small">
          <Descriptions.Item label="许可编号">(晋) WH安许证 [2024] 235B2Y1号</Descriptions.Item>
          <Descriptions.Item label="许可证书名称">危险化学品企业安全生产许可证</Descriptions.Item>
          <Descriptions.Item label="企业名称">稷山永东化学股份有限公司</Descriptions.Item>
          <Descriptions.Item label="主要负责人">刘东杰</Descriptions.Item>
          <Descriptions.Item label="单位地址">
            山西省运城市稷山县稷山经济技术开发区振西大街东
          </Descriptions.Item>
          <Descriptions.Item label="经济类型">有限责任公司</Descriptions.Item>
          <Descriptions.Item label="许可机关">山西省应急管理厅</Descriptions.Item>
          <Descriptions.Item label="发证日期">2020-1-14</Descriptions.Item>
          <Descriptions.Item label="许可有效期自">2020-1-14</Descriptions.Item>
          <Descriptions.Item label="许可有效期至">
            <span style={{ color: '#ff4d4f' }}>2025-1-13</span>
          </Descriptions.Item>
          <Descriptions.Item label="许可状态">
            <span style={{ color: '#ff4d4f' }}>过期</span>
          </Descriptions.Item>
          <Descriptions.Item label="许可类别">普通许可</Descriptions.Item>
          <Descriptions.Item label="许可范围" span={2}>
            危险化学品生产
          </Descriptions.Item>
          <Descriptions.Item label="附件" span={2}>
            永东危化品生产许可证.jpg
          </Descriptions.Item>
        </Descriptions>
      );
    }
  };

  return (
    <Modal
      title={data.certificateName}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      destroyOnClose
    >
      {renderContent()}
    </Modal>
  );
};

export default CertificateDetailModal;
