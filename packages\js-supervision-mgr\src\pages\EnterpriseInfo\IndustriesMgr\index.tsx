import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import ChildIndustriesForm from './components/ChildIndustriesForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import {
  deleteIndustries,
  getIndustriesDetail,
  saveIndustries,
} from '@/services/companyInfo/industriesMgr';
import { EditPageTitle } from '@/utils/constants';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

/**
 * 子行业新增情形要利用表单的 edit 模式注入父行业的一些信息
 * 单纯使用 openType 无法进行精准控制，所以加一个参数控制
 */
let isChildAdd = false;

const IndustriesMgr: React.FC = () => {
  const {
    id,
    updateTrigger,
    openType,
    initValue,
    formTitle,
    setFormTitle,
    formVisible,
    setInitValue,
    setFormVisible,
    selectedKeys,
    formLoading,
    openFormWithAdd,
    deleteHandler,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    setOpenType,
  } = useCommonFormPage({ HomeTitle });
  const [formTarget, setFormTarget] = useState<React.ReactNode>(
    <BaseInfoForm openType={openType} />,
  );

  customButtons[0].onClick = () => {
    isChildAdd = false;
    openFormWithAdd();
    setFormTarget(<BaseInfoForm openType={openType} />);
  };

  customButtons[1].onClick = () => {
    deleteHandler(selectedKeys, deleteIndustries);
  };

  const saveIndustriesInfo = async (formData: any) => {
    const values = await saveHandler(formData, saveIndustries, data => {
      const newData = { ...data };
      if (openType === 'edit' && !isChildAdd) {
        newData.id = id;
      } else Reflect.deleteProperty(newData, 'id');
      return newData;
    });
    return values;
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getIndustriesDetail, data => {
      const { parentId } = data;
      if (parentId) {
        setFormTarget(<ChildIndustriesForm openType={openType} />);
      }
      return data;
    });
    isChildAdd = false;
    setFormTarget(<BaseInfoForm openType={openType} />);
  };

  const openChildIndustries = (record: any) => {
    setOpenType('edit'); // add 模式无法注入 initValue，利用 edit 模式注入
    setFormVisible(true);
    setFormTitle(EditPageTitle);
    isChildAdd = true;
    const initValue = { parentName: record.subName, parentId: record.id };

    setInitValue(initValue);
    setFormTarget(<ChildIndustriesForm openType={openType} />);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      width: 210,
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openChildIndustries(record)}>
            新增子行业
          </CommonLinkButton>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton>风险清单</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={saveIndustriesInfo}
      formComp={formTarget}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/changeIndustries/industriesList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default IndustriesMgr;
