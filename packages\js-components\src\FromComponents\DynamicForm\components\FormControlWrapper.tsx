import React from 'react';
import { FormOpenType } from '../../FormContainer';

interface FormControlWrapperProps {
  children: any;
  extraContent?: React.ReactNode;
  openType?: FormOpenType; // 可选，表示表单的操作类型
  [key: string]: any; // 接收 antd Form.Item 注入的所有 props
}

const FormControlWrapper: React.FC<FormControlWrapperProps> = ({
  children,
  openType,
  extraContent,
  ...restProps // 这里就是 antd 注入的 value、onChange 等
}) => {
  // 用 cloneElement 把这些 props 传递给 children（即表单控件）
  const control = React.cloneElement(children, { ...restProps });

  return (
    <>
      {control}
      {openType !== 'view' && extraContent}
    </>
  );
};

export default FormControlWrapper;
