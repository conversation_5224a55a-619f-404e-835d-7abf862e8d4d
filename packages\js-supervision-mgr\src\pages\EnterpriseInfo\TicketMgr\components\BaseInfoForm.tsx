import React from 'react';
import { DynamicForm, DynamicFormItem, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
  formItems: DynamicFormItem[];
  formInitValue?: any;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({
  openType,
  form,
  formItems,
  formInitValue,
}) => {
  return (
    <>
      <DynamicForm
        items={formItems}
        openType={openType}
        form={form}
        formInitValue={formInitValue}
      />
    </>
  );
};

export default BaseInfoForm;
