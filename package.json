{"name": "new-manager", "version": "1.0.0", "private": true, "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "turbo dev", "build": "turbo build"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@8.15.9", "devDependencies": {"@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@whalecloud/eslint-config": "^0.0.40-beta.10", "eslint-plugin-prettier": "^5.1.3", "@umijs/lint": "^4.1.1", "father": "^4.5.6", "turbo": "^2.5.5", "eslint": "^8.56.0", "typescript": "^5.3.3"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "antd": "^5.13.2"}, "workspaces": ["packages/*"], "engines": {"node": ">=18.0.0 <19.0.0"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["react", "react-dom", "@babel/core", "antd", "dva", "postcss", "webpack", "stylelint", "redux", "prettier", "@types/react", "rc-field-form", "@types/react", "eslint", "typescript", "@types/node"]}}}