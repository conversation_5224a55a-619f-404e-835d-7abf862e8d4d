import React from 'react';
import { CustomButton, SearchInput, JSCUtils } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

const { getTemplateDownloadUrl } = JSCUtils;

export const HomeTitle = '重大危险源备案管理';

export const exportUrl = `${SPPREFIX}/basicinformation/sourceManagement/exportSource`;

export const importUrl = `${SPPREFIX}/basicinformation/sourceManagement/importSource`;

export const exportTitle = '重大危险源备案表';

const urlArray: [string, string] = [
  `${HomeTitle}.xlsx`,
  '20250717/cf718c84-2b00-4f2d-990f-4625c8d50fbc.xlsx',
];

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    span: 4,
    component: <SearchInput placeholder="请输入企业名称" style={{ width: '100%' }} />,
  },
  {
    name: 'hazardName',
    label: '危险源名称',
    span: 4,
    component: <SearchInput placeholder="请输入危险源名称" style={{ width: '100%' }} />,
  },
  {
    name: 'hazardLevel',
    label: '危险源等级名称',
    span: 4,
    component: <SearchInput placeholder="请选择危险源等级" style={{ width: '100%' }} />,
  },
  {
    name: 'hazardType',
    label: '危险源类型名称',
    span: 4,
    component: <SearchInput placeholder="请选择危险源类型" style={{ width: '100%' }} />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle, marginRight: 4 },
  { text: '删除', ...baseButtonStyle, marginRight: 4 },
  { text: '导入', ...baseButtonStyle, marginRight: 4 },
  { text: '导出', ...baseButtonStyle, marginRight: 4 },
  {
    text: '模板下载',
    ...baseButtonStyle,
    marginRight: 4,
    onClick: () => window.open(getTemplateDownloadUrl(...urlArray), '_blank'),
  },
];

export const tableColumns: any[] = [
  { title: '企业名称', dataIndex: 'enterpriseName', width: 180 },
  { title: '危险源名称', dataIndex: 'hazardName', width: 180 },
  { title: '危险源类型名称', dataIndex: 'hazardType', width: 140 },
  { title: '危险源等级名称', dataIndex: 'hazardLevel', width: 140 },
  { title: 'R值', dataIndex: 'rValue', width: 80 },
  { title: '重大危险源辨识结论', dataIndex: 'identificationConclusion' },
  { title: '重大危险源分级依据', dataIndex: 'classificationBasis' },
];
