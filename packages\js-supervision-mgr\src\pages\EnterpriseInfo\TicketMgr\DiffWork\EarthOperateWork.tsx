import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  getLocationItem,
  OpinionItem,
  getLocationColumn,
} from '../constants';
import { DynamicFormItem, RULES } from 'jishan-components';
import { checkOpinionAndName, getOpinionItems, setSignTimeWrapper } from '../utils';
import SafetyMeasuresList from '../components/Measures';
import { Modal } from 'antd';

const { textAreaLengthRules, nameLengthRules } = RULES;

const HomeTitle = '动土作业';

const newColumns = [...tableColumns];
newColumns.splice(3, 0, getLocationColumn('作业地点'), {
  title: '作业内容',
  dataIndex: 'workContent',
  width: 150,
});

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.workUnit,
  FrequencyItems.applyTime,
  { quickItemParams: ['作业地点', 'workLocation', 'Input', true], rules: [nameLengthRules] },
  FrequencyItems.workContent,
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  FrequencyItems.supervisor,
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  FrequencyItems.actualTime,
  {
    quickItemParams: [
      '作业范围、内容、方式（包括深度、面积、并附件图）',
      'scopeWork',
      'TextArea',
      true,
    ],
    rules: [textAreaLengthRules],
    span: 16,
    labelCol: { span: 10 },
  },
  {
    quickItemParams: ['', 'scopeWorkFile', 'FileUpload', true, '请上传图片'],
    span: 3,
    uploadFileInfo: {
      tip: '只能上传jpg、png格式',
      targetType: '.png,.jpg',
      buttonTitle: '点击上传图片',
    },
  },
  {
    quickItemParams: ['', 'scopeWorkSignature', 'Sign', true, '请签名'],
    span: 4,
    signInfo: { signCallback: setSignTimeWrapper('scopeWorkTime') },
  },
  { type: 'hideItem', name: 'scopeWorkTime' },
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  ...OpinionItem.jobOpinion,
  ...OpinionItem.unitOpinion,
  ...getOpinionItems('有关水、电、汽、工艺、设备、消防、安全等部门会签意见', 'department', {
    labelCol: { span: 12 },
  }),
  ...getOpinionItems('审批部门意见', 'openFire'),
  ...OpinionItem.completedOpinion,

  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'blindDrawingName' },
];

const opinionList: [string, string][] = [
  ['department', '有关水、电、汽、工艺、设备、消防、安全等部门会签意见'],
];

const EarthOperateWork: React.FC = () => {
  const checkOpinion = (data: any) => {
    const checkRes = checkOpinionAndName(opinionList, data);
    if (typeof checkRes === 'boolean') return data;
    Modal.error({
      title: '校验未通过',
      content: (
        <div>
          {checkRes.map((err, idx) => (
            <div key={idx}>{err}</div>
          ))}
        </div>
      ),
    });
    return false;
  };

  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[getLocationItem('作业地点'), applyTimeItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      keyName={HomeTitle}
      formItems={formItems}
      imgNames={['scopeWorkFile']}
      handleDataBeforeSave={checkOpinion}
    />
  );
};

export default EarthOperateWork;
