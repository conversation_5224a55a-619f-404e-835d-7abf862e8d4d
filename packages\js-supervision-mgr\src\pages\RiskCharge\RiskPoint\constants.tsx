import React from 'react';
import { CustomButton, SearchInput, JSCUtils } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';
import { levelColumns as LColumns } from '@/pages/RiskLevelMgr/RiskLevelList/constants';

const { getTemplateDownloadUrl } = JSCUtils;

export const HomeTitle = '风险点管理';

export const exportTitle = HomeTitle;

export const exportUrl = `${SPPREFIX}/risk/point/exportRiskPoint`;

export const filterItems = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    component: <SearchInput placeholder="请输入企业名称" />,
  },
  {
    name: 'riskName',
    label: '风险点名称',
    component: <SearchInput placeholder="请输入风险点名称" />,
    span: 5,
  },
  {
    name: 'riskLevel',
    label: '风险点级别',
    component: <SearchInput placeholder="请输入风险点级别" />,
  },
];

const urlArray: [string, string] = [
  `${HomeTitle}.xlsx`,
  '20250729/476f2345-b9a8-46d8-a406-5fd01ae93723.xlsx',
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '导入', ...baseButtonStyle },
  { text: '导出', ...baseButtonStyle },
  {
    text: '模板下载',
    ...baseButtonStyle,
    onClick: () => window.open(getTemplateDownloadUrl(...urlArray), '_blank'),
  },
];

export const tableColumns = [
  { title: '企业名称', dataIndex: 'enterpriseName', width: 180 },
  { title: '风险点名称', dataIndex: 'riskName', width: 150 },
  { title: '风险点类型', dataIndex: 'riskType', width: 120 },
  { title: '风险点级别', dataIndex: 'riskLevel', width: 100 },
  { title: '安全责任人', dataIndex: 'safetyPrincipal', width: 120 },
  { title: '风险辨识人员', dataIndex: 'riskIdentifier', width: 120 },
  { title: '风险辨识责任人', dataIndex: 'identificationPrincipal', width: 120 },
  { title: '风险辨识审核人', dataIndex: 'riskReviewer', width: 120 },
  { title: '主要后果', dataIndex: 'mainConsequence', width: 220 },
  { title: '危害分析', dataIndex: 'riskFactor', width: 220 },
];

export const levelColumns = LColumns;
