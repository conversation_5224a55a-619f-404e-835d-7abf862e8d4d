import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { getriskTypeList } from '@/services/riskCharge/riskType';

const { nameLengthRules, phoneRules, phoneLengthRules, textAreaLengthRules } = RULES;

const baseCompanyItem: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
  },
  { type: 'hideItem', name: 'industryCategory', relatedValue: 'enterpriseName.industryCategory' },
  { type: 'hideItem', name: 'enterpriseCode', relatedValue: 'enterpriseName.enterpriseCode' },
];

const industriesItem: DynamicFormItem[] = [
  {
    quickItemParams: ['所属行业', 'subName', 'Input', false, '自动带出'],
  },
];

const formItems: DynamicFormItem[] = [
  { quickItemParams: ['风险点名称', 'riskName', 'Input', true], rules: [nameLengthRules] },
  {
    quickItemParams: ['风险类型', 'riskType', 'Select', true],
    optionsServices: () => fetchSelectOptions(getriskTypeList, 'typeName', 'typeCode'),
  },
  { quickItemParams: ['风险级别', 'riskLevel', 'Input', true] },
  { quickItemParams: ['风险点编号', 'riskCode', 'Input', false, '自动生成'], disabled: true },
  { quickItemParams: ['风险负责人', 'safetyPrincipal', 'Input', false] },
  {
    quickItemParams: ['负责人联系方式', 'principalContact', 'Input', false],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['风险因子', 'riskFactor', 'Input', true] },
  { quickItemParams: ['后果', 'mainConsequence', 'Input', false] },
  {
    quickItemParams: ['应急处置建议', 'emergencyMeasures', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  { quickItemParams: ['风险地址', 'riskLocation', 'Input', false] },
  { quickItemParams: ['风险情景', 'riskScenario', 'Input', false] },
  { type: 'hideItem', name: 'enterpriseCode' },
  { type: 'hideItem', name: 'industryCategory' },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      {openType === 'add' && (
        <DynamicForm items={baseCompanyItem} openType={openType} form={form} />
      )}
      <DynamicForm items={formItems} openType={openType} form={form} />
      {openType === 'view' && (
        <DynamicForm items={industriesItem} openType={openType} form={form} />
      )}
    </>
  );
};

export default BaseInfoForm;
