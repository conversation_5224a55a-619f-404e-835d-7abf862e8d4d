import { request } from '@/utils/net';

export async function saveRiskList(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/risk/list/saveRiskList`, params);
}

export async function getRiskListDetail(id: number) {
  return request.get(`${SPPREFIX}/risk/list/riskListDetail`, {
    id,
  });
}

export async function deleteRiskList(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/risk/list/removeRiskList`, params);
}
