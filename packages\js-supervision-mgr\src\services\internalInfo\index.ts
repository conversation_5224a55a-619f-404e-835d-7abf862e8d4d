import { request } from '@/utils/net';

export async function saveInternalInfo(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/basicinformation/internal/saveInternal`, params);
}

export async function getInternalDetail(id: number) {
  return request.get(`${SPPREFIX}/basicinformation/internal/internalDetail`, {
    id,
  });
}

export async function deletePersonInfo(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/basicinformation/internal/removeInternal`, params);
}
