{"name": "js-supervision-mgr", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "packageManager": "pnpm@8.15.9", "dependencies": {"@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.48", "@umijs/route-utils": "^2.2.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ace-builds": "^1.39.1", "antd-img-crop": "^4.23.0", "antd-style": "^3.6.1", "classnames": "^2.5.1", "cron-parser": "^4.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "eslint-plugin-prettier": "^5.1.3", "lodash": "^4.17.21", "moment": "^2.30.1", "query-string": "^9.1.1", "rc-menu": "^9.12.4", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "umi-request": "^1.4.0", "uuid": "^10.0.0", "jishan-components": "workspace:*"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/history": "^4.7.11", "@types/lodash": "^4.14.202", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.1.1", "@umijs/max": "^4.1.1", "@whalecloud/eslint-config": "^0.0.40-beta.10", "cross-env": "^7.0.3", "echarts": "^5.5.0", "eslint": "^8.56.0", "husky": "^7.0.4", "prettier": "^2.8.8", "ts-node": "^10.9.2", "typescript": "^5.3.3", "umi-presets-pro": "^2.0.3"}, "peerDependencies": {"react": "^18.2.0", "antd": "^5.13.2", "react-dom": "^18.2.0"}, "engines": {"node": ">=18.0.0"}, "eslintConfig": {"extends": ["../../.eslintrc.js"]}}