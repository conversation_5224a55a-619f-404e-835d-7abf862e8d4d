/* eslint-disable */

import { sysDictList } from '@/services/common/common';
import { sortBy } from 'lodash';
import { useCallback, useEffect, useState } from 'react';

export default function useDict(classCodes: string, dependencies = []) {
  const [value, setValue] = useState<any[]>([]);
  const [valueMap, setValueMap] = useState<any>({});

  const callbackMemoized = useCallback(() => {
    setValue([]);
    setValueMap({});
    sysDictList({ classCodes }).then(({ success, data }) => {
      if (success) {
        const tempMap: any = {};
        setValue(sortBy(data, 'sortIndex'));
        data?.map((item: any) => {
          tempMap[item.dictCode] = item.dictValue;
        });
        setValueMap(tempMap);
      }
    });
  }, [classCodes, ...dependencies]);

  useEffect(() => {
    callbackMemoized();
  }, [callbackMemoized]);

  return { value, valueMap };
}
