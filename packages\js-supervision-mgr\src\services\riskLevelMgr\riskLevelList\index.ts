import { request } from '@/utils/net';

export async function saveAssessment(params: Record<string, any>) {
  return request.post(`${SPPREFIX}/riskLevel/scaleList/saveAssessment`, params);
}

export async function getAssessmentDetail(id: number) {
  return request.get(`${SPPREFIX}/riskLevel/scaleList/assessmentDetail`, {
    id,
  });
}

export async function deleteAssessment(params: Record<'idParams', { id: number | string }[]>) {
  return request.post(`${SPPREFIX}/riskLevel/scaleList/removeAssessment`, params);
}

export async function getScoreList() {
  return request.get(`${SPPREFIX}/riskLevel/scaleList/ScoreList?pageNo=1&pageSize=100`);
}
