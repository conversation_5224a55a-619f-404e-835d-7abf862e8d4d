.enterprise-statistics-cards {
  width: 100%;

  .enterprise-statistics-card {
    display: flex;
    align-items: center;
    padding: 0px 20px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 100px;
    background-color: #fff;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .enterprise-statistics-card-icon {
      flex-shrink: 0;
      margin-right: 16px;
      font-size: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 8px;
      color: #fff;

      .anticon {
        font-size: 24px;
      }
    }

    // 不同卡片的图标背景色
    &[data-card-id='enterprise-group'] .enterprise-statistics-card-icon {
      background-color: #52c41a;
    }

    &[data-card-id='certificate-group'] .enterprise-statistics-card-icon {
      background-color: #1890ff;
    }

    &[data-card-id='personnel-group'] .enterprise-statistics-card-icon {
      background-color: #fa8c16;
    }

    &[data-card-id='equipment-group'] .enterprise-statistics-card-icon {
      background-color: #eb2f96;
    }

    .enterprise-statistics-card-content {
      flex: 1;
      display: flex;
      flex-direction: row;
      gap: 16px;
      min-width: 0;

      .enterprise-statistics-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        padding: 8px 4px;
        transition: opacity 0.2s ease;
        flex: 1;
        text-align: center;

        &:hover {
          opacity: 0.8;
        }

        .enterprise-statistics-count {
          font-size: 28px;
          font-weight: bold;
          line-height: 1.2;
          font-family: 'DINAlternate-Bold', 'Arial', sans-serif;
          margin-bottom: 4px;
          color: #333;

          &.warning {
            color: #ff4d4f;
          }
        }

        .enterprise-statistics-title {
          font-size: 12px;
          line-height: 1.4;
          color: #666;
          word-break: break-all;
          text-align: center;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .enterprise-statistics-cards {
    .enterprise-statistics-card {
      padding: 12px 16px;
      min-height: 70px;

      .enterprise-statistics-card-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;

        .anticon {
          font-size: 20px;
        }
      }

      .enterprise-statistics-card-content {
        gap: 12px;

        .enterprise-statistics-item {
          padding: 6px 2px;

          .enterprise-statistics-count {
            font-size: 22px;
          }

          .enterprise-statistics-title {
            font-size: 11px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .enterprise-statistics-cards {
    .enterprise-statistics-card {
      padding: 10px 12px;
      min-height: 60px;

      .enterprise-statistics-card-icon {
        width: 36px;
        height: 36px;
        margin-right: 10px;

        .anticon {
          font-size: 18px;
        }
      }

      .enterprise-statistics-card-content {
        gap: 8px;

        .enterprise-statistics-item {
          padding: 4px 2px;

          .enterprise-statistics-count {
            font-size: 18px;
            margin-bottom: 2px;
          }

          .enterprise-statistics-title {
            font-size: 10px;
          }
        }
      }
    }
  }
}
