﻿export default [
  {
    path: '/',
    routes: [
      {
        name: 'login',
        path: '/login',
        layout: false,
        component: '@/pages/Login',
      },
      {
        path: '/columnDetail',
        component: '@/pages/HomeColumnDetails',
      },
      {
        path: '/homePreviewPage',
        component: '@/pages/HomePreviewPage',
      },
      {
        path: '/homePage',
        name: 'homePage',
        icon: 'smile',
        redirect: '/outsourcingPeople',
      },
      {
        path: '/companyBaseInfoMgr',
        name: '企业基本信息管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/CompanyBaseInfoMgr',
      },
      {
        path: '/internalMgr',
        name: '内部管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/InternalMgr',
      },
      {
        path: '/certificateMgr',
        name: '资质证照维护',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/CertificateMgr',
      },
      {
        path: '/meantimeMgr',
        name: '三同时管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/MeantimeMgr',
      },
      {
        path: '/industriesMgr',
        name: '变更行业',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/IndustriesMgr',
      },
      {
        path: '/sourceMgr',
        name: '重大危险源备案管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/SourceMgr',
      },
      {
        path: '/chemicalsMgr',
        name: '重点危化品信息',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/ChemicalsMgr',
      },
      {
        path: '/processMgr',
        name: '生产工艺信息',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/ProcessMgr',
      },
      {
        path: '/workerMgr',
        name: '企业人员管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/WorkerMgr',
      },
      {
        path: '/emergencyPlanMgr',
        name: '应急预案备案管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/EmergencyPlanMgr',
      },
      {
        path: '/hiddenDangerReport',
        name: '企业自查隐患上报',
        icon: 'smile',
        component: '@/pages/HiddenDanger/HiddenDangerReport',
      },
      {
        path: '/outsourcingPeopleMgr',
        name: '外包人员管理',
        icon: 'smile',
        component: '@/pages/EnterpriseInfo/OutsourcingPeopleMgr',
      },
      {
        path: '/spaceWork',
        name: '受限空间作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/SpaceWork',
      },
      {
        path: '/fireWork',
        name: '动火作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/FireWork',
      },
      {
        path: '/blindPlateWork',
        name: '盲板抽堵作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/BlindPlateWork',
      },
      {
        path: '/highPlaceWork',
        name: '高处作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/HighPlaceWork',
      },
      {
        path: '/hoistingWork',
        name: '吊装作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/HoistingWork',
      },
      {
        path: '/electricityWork',
        name: '临时用电作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/ElectricityWork',
      },
      {
        path: '/earthOperateWork',
        name: '动土作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/EarthOperateWork',
      },
      {
        path: '/circuitBreakerWork',
        name: '断路作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/CircuitBreakerWork',
      },
      {
        path: '/blastingWork',
        name: '爆破作业',
        component: '@/pages/EnterpriseInfo/TicketMgr/DiffWork/BlastingWork',
      },
      {
        path: '/riskList',
        name: '风险清单',
        component: '@/pages/RiskCharge/RiskList',
      },
      {
        path: '/riskPoint',
        name: '风险点管理',
        component: '@/pages/RiskCharge/RiskPoint',
      },
      {
        path: '/riskType',
        name: '风险类型',
        component: '@/pages/RiskCharge/RiskType',
      },
      {
        path: '/riskLevelList',
        name: '风险分级清单',
        component: '@/pages/RiskLevelMgr/RiskLevelList',
      },
      {
        path: '/measureList',
        name: '风险分级清单',
        component: '@/pages/RiskLevelMgr/MeasureList',
      },
      {
        path: '/gradingList',
        name: '风险分级管控清单',
        component: '@/pages/RiskLevelMgr/GradingList',
      },
      {
        path: '/riskControl',
        name: '风险掌控',
        component: '@/pages/RiskControl',
      },
      {
        path: '/positionList',
        name: '岗位维护',
        component: '@/pages/PositionRisk/PositionList',
      },
      {
        path: '/checkList',
        name: '岗位排查清单',
        component: '@/pages/PositionRisk/CheckList',
      },
      {
        path: '/safetyProduction',
        redirect: '/enterpriseArchive',
      },
      {
        path: '/enterpriseArchive',
        name: '一企一档',
        component: '@/pages/WorkSafety/EnterpriseArchive',
      },
    ],
  },

  {
    path: '/',
    redirect: '/companyBaseInfoMgr',
  },

  {
    path: '/jssupervision/',
    redirect: '/companyBaseInfoMgr',
  },

  {
    path: '/jssupervision/safetyProduction/',
    redirect: '/enterpriseArchive',
  },

  {
    path: '*',
    layout: false,
    component: '@/pages/404',
  },
];
