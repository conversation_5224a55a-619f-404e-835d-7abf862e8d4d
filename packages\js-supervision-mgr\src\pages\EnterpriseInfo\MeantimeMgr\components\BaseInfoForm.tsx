import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, JSCUtils, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams } from '@/utils/constants';

const {
  nameLengthRules,
  codeLengthRules,
  phoneLengthRules,
  phoneRules,
  codeRules,
  textAreaLengthRules,
  postalCodeRules,
} = RULES;
const { setOption } = JSCUtils;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['项目名称', 'projectName', 'Input', true],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['建设单位', 'constructionUnit', 'ModalSelect', true],
    rules: [nameLengthRules],
    modalSelectInfo: companyModalSelectParams,
  },
  {
    quickItemParams: ['建设性质', 'constructionProperty', 'Select', true],
    options: [
      setOption('新建'),
      setOption('改建'),
      setOption('扩建'),
      setOption('技术改造'),
      setOption('技术引进'),
    ],
  },
  {
    quickItemParams: ['开工日期', 'openingDate', 'DatePicker', false],
  },
  {
    quickItemParams: ['项目负责人', 'projectLeader', 'Input', true],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['联系电话', 'contactPhone', 'Input', true],
    rules: [phoneRules, phoneLengthRules],
  },
  {
    quickItemParams: ['建设地址', 'constructionAddress', 'TextArea', true],
    rules: [textAreaLengthRules],
  },
  {
    quickItemParams: ['邮政编码', 'zipCode', 'Input', false],
    rules: [postalCodeRules],
  },
  {
    quickItemParams: ['投资总概算（万元）', 'totalInvestment', 'FloatInput', true],
    decimalPlaces: 4,
  },
  {
    quickItemParams: ['实际总投资（万元）', 'actualInvestment', 'FloatInput', false],
    decimalPlaces: 4,
  },
  {
    quickItemParams: ['立项审批/核准/备案机关', 'approvalAuthority', 'Input', false],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['批文文号', 'approvalDocument', 'Input', false],
  },
  {
    quickItemParams: ['设立审查或备案', 'establishmentFiling', 'Input', false],
  },
  {
    quickItemParams: ['设立审查或备案时间', 'establishmentFilingDate', 'DatePicker', false],
  },
  {
    quickItemParams: ['设立审查或备案编号', 'establishmentApprovalNo', 'Input', false],
    rules: [codeRules, codeLengthRules],
  },
  {
    quickItemParams: ['设立审查或备案机关', 'establishmentApprovalAuthority', 'Input', false],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['设立审查或备案附件', 'establishmentApprovalAttach', 'FileUpload', false],
  },
  {
    quickItemParams: ['试运行备案', 'trialSupervisionNo', 'Input', false],
  },
  {
    quickItemParams: [
      '试生产（使用）期限起',
      'trialProductionPeriodStart',
      'DatePicker',
      false,
      '请选择开始时间',
    ],
  },
  {
    quickItemParams: [
      '试生产（使用）期限至',
      'trialProductionPeriodEnd',
      'DatePicker',
      false,
      '请选择结束时间',
    ],
  },
  {
    quickItemParams: ['试运行备案机关', 'trialSupervisionAuthority', 'Input', false],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['试运行备案附件', 'trialSupervisionAttach', 'FileUpload', false],
  },
  {
    quickItemParams: ['竣工验收', 'completionInspectionNo', 'Input', false],
  },
  {
    quickItemParams: ['竣工验收时间', 'completionInspectionDate', 'DatePicker', false],
  },
  {
    quickItemParams: ['竣工验收机关', 'completionInspectionAuthority', 'Input', false],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['竣工验收附件', 'completionInspectionAttach', 'FileUpload', false],
  },
  { name: 'enterpriseCode', type: 'hideItem', relatedValue: 'constructionUnit.enterpriseCode' },
];

export const fileNames = [
  'establishmentApprovalAttach',
  'trialSupervisionAttach',
  'completionInspectionAttach',
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
