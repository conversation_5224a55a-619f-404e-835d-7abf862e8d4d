import React from 'react';
import { CustomButton, SearchInput } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';

export const HomeTitle = '三同时管理';

export const filterItems = [
  {
    name: 'projectName',
    label: '项目名称',
    component: <SearchInput placeholder="请输入项目名称" />,
  },
  {
    name: 'constructionUnit',
    label: '建设单位',
    component: <SearchInput placeholder="请输入建设单位" />,
  },
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
];

export const tableColumns: any[] = [
  { title: '项目名称', dataIndex: 'projectName', width: 180 },
  { title: '建设单位名称', dataIndex: 'constructionUnit' },
  { title: '建设性质', dataIndex: 'constructionProperty' },
  { title: '开工日期', dataIndex: 'openingDate' },
  { title: '项目负责人', dataIndex: 'projectLeader' },
  { title: '联系电话', dataIndex: 'contactPhone' },
  { title: '建设地址', dataIndex: 'constructionAddress' },
  { title: '状态', dataIndex: 'state' },
];
