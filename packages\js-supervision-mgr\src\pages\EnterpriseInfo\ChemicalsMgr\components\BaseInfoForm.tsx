import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';
import { companyModalSelectParams, defaultRadioOptions } from '@/utils/constants';

const { nameLengthRules } = RULES;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    rules: [nameLengthRules],
    modalSelectInfo: companyModalSelectParams,
  },
  { quickItemParams: ['危化品名称', 'chemicalName', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['分类', 'classification', 'Input', false] },
  { quickItemParams: ['CAS', 'casNumber', 'Input', false] },
  { quickItemParams: ['UN号', 'unNumber', 'Input', false] },
  { quickItemParams: ['别名', 'alias', 'Input', false], rules: [nameLengthRules] },
  { quickItemParams: ['危规号', 'dgrNumber', 'Input', false] },
  {
    quickItemParams: ['是否重点监管', 'isKeySupervision', 'Radio', false],
    options: defaultRadioOptions,
  },
  { quickItemParams: ['阶段', 'stage', 'Input', false] },
  { name: 'enterpriseCode', type: 'hideItem', relatedValue: 'enterpriseName.enterpriseCode' },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
