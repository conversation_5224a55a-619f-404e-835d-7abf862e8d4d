import React from 'react';
import { DynamicForm, DynamicFormItem, SectionTitle, RULES } from 'jishan-components';
import { fetchCascaderOptions } from '@/utils/commonFunction';
import { qryAreaTree } from '@/services/common/common';

const { textAreaLengthRules } = RULES;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['注册日期', 'registrationDate', 'DatePicker', true],
  },
  {
    quickItemParams: ['注册资本', 'registeredCapital', 'FloatInput', false],
    decimalPlaces: 2,
  },
  {
    quickItemParams: ['注册地省/市/地区/县', 'tempAreaData', 'Cascader', false],
    cascaderOptionsServices: () =>
      fetchCascaderOptions(qryAreaTree, 'areaName', 'areaCode', 'childrenList'),
  },
  {
    quickItemParams: ['企业注册地址', 'registeredAddress', 'Input', false],
    rules: [textAreaLengthRules],
  },
];

const addrNames = [
  'registeredDistrictProvince',
  'registeredDistrictCity',
  'registeredDistrictCounty',
  'registeredDistrictVillages',
];

export const RegisterInfoFormNames = [...formItems.map(item => item.name), ...addrNames];

export const cascadeFieldsMap = { tempAreaData: addrNames };

const RegisterInfoForm: React.FC = () => {
  return (
    <>
      <SectionTitle title="注册信息" />
      <DynamicForm items={formItems} />
    </>
  );
};

export default RegisterInfoForm;
