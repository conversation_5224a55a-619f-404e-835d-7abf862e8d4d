import { downloadFile } from '@/utils/commonFileFun';
import { Button, Modal } from 'antd';
// @ts-ignore
import CryptoJS from 'crypto-js';
import { JSCUtils } from 'jishan-components';
import React, { useEffect, useState } from 'react';
import './index.less';

const { getTemplateDownloadUrl } = JSCUtils;

interface FilePreviewProps {
  path: string;
  open?: boolean;
  onClose?: () => void;
  width?: number;
  showDownloadBtn?: boolean;
  attachName?: string;
  savePath?: string;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  path,
  open,
  onClose,
  width,
  showDownloadBtn = false,
  attachName = '',
  savePath = '',
}) => {
  const [title, setTitle] = useState('');
  const [filePreviewUrl, setFilePreviewUrl] = useState('');

  useEffect(() => {
    if (path) {
      const urlParams = new URLSearchParams(path);
      const fileName = urlParams.get('fileName');
      setTitle(fileName as string);
      const trans = CryptoJS.enc.Utf8.parse(downloadFile(path));
      setFilePreviewUrl(
        `${PRIVIEW_SERVER}?url=${encodeURIComponent(CryptoJS.enc.Base64.stringify(trans))}`,
      );
    }
  }, [path]);

  const onDownload = () => {
    const urlArray: [string, string] = [`${attachName}`, savePath];
    window.open(getTemplateDownloadUrl(...urlArray), '_blank');
  };

  return (
    <Modal
      className="file-preview-modal"
      title={title}
      width={width || 854}
      styles={{ body: { height: 600 } }}
      open={open}
      onCancel={onClose}
      footer={[
        <div style={{ flex: 1 }} key="container" />,
        showDownloadBtn ? (
          <Button key="download" onClick={onDownload}>
            下载
          </Button>
        ) : (
          ''
        ),
        <Button onClick={onClose} key="button">
          关闭
        </Button>,
      ]}
    >
      <iframe title="preview" src={filePreviewUrl} height="100%" width="100%" />
    </Modal>
  );
};

export default FilePreview;
