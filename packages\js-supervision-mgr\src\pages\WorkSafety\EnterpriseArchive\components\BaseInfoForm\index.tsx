import React, { useState } from 'react';
import { Card, Descriptions, Tabs, Badge } from 'antd';
import BusinessRegistration from '../BusinessRegistration';
import InternalManagement from '../InternalManagement';
import Qualification from '../Qualification';
import Personnel from '../Personnel';
import SafetyProduction from '../SafetyProduction';
import SpecialOperation from '../SpecialOperation';
import Explosives from '../Explosives';
import OutsourcingCompany from '../OutsourcingCompany';
import './index.less';

// 企业基本信息数据接口
interface EnterpriseInfo {
  // 基本信息
  enterpriseName: string;
  unifiedSocialCreditCode: string;
  nationalEconomicIndustryCode: string;
  enterpriseEconomicType: string;

  // 人员和地址信息
  employeeCount: number;
  registeredAddress: string;
  legalRepresentative: string;
  businessRegistrationStatus: string;

  // 经营信息
  businessPeriod: string;
  businessScope: string;
  industryCategory: string;
  currentStatus: string;

  // 许可范围
  permittedScope: string;
}

interface BaseInfoFormProps {
  data?: EnterpriseInfo;
}

// 默认数据
const defaultEnterpriseData: EnterpriseInfo = {
  enterpriseName: '梅山县永升化工股份有限公司',
  unifiedSocialCreditCode: '91114800071986164SD',
  nationalEconomicIndustryCode: '2664',
  enterpriseEconomicType: '私营经济',
  employeeCount: 865,
  registeredAddress: '梅山县西化工园区',
  legalRepresentative: '刘东杰',
  businessRegistrationStatus: '存续（在营、开业、在册）',
  businessPeriod: '2000-05-20 至 无固定期限',
  businessScope: '水泥制品',
  industryCategory: '危险化学品',
  currentStatus: '—',
  permittedScope:
    '危险化学品生产、（农药原料批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）批发无储存设施（不含危险化学品）；化工产品销售（不含危险化学品）；专用化学产品制造（不含危险化学品）；专用化学产品销售（不含危险化学品）；石油及其制品制造；石油及其制品销售；合成材料制造；热力生产和供应；条件类条件气体技术开发；技术服务、技术开发、技术咨询、技术转让；原材料技术开发；新材料技术推广服务；保温制品销售；土地使用权租赁；货物进出口；技术进出口。',
};

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ data = defaultEnterpriseData }) => {
  const [activeTab, setActiveTab] = useState('business-registration');

  return (
    <div className="base-info-form">
      {/* 企业基本信息 - 使用 Descriptions */}
      <Card title="企业基本信息" className="enterprise-info-card">
        <Descriptions
          bordered
          size="middle"
          column={{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}
          labelStyle={{
            backgroundColor: '#fafafa',
            fontWeight: 500,
            color: '#666',
            width: '140px',
          }}
          contentStyle={{
            backgroundColor: '#fff',
            color: '#262626',
          }}
        >
          <Descriptions.Item label="生产经营单位名称">{data.enterpriseName}</Descriptions.Item>
          <Descriptions.Item label="职工人数">{data.employeeCount}</Descriptions.Item>
          <Descriptions.Item label="经营期限">{data.businessPeriod}</Descriptions.Item>

          <Descriptions.Item label="统一社会信用代码">
            {data.unifiedSocialCreditCode}
          </Descriptions.Item>
          <Descriptions.Item label="上级主管部门">{data.registeredAddress}</Descriptions.Item>
          <Descriptions.Item label="营业执照">{data.businessScope}</Descriptions.Item>

          <Descriptions.Item label="国民经济行业编码">
            {data.nationalEconomicIndustryCode}
          </Descriptions.Item>
          <Descriptions.Item label="法定代表人">{data.legalRepresentative}</Descriptions.Item>
          <Descriptions.Item label="行业分类">{data.industryCategory}</Descriptions.Item>

          <Descriptions.Item label="企业经济类型">{data.enterpriseEconomicType}</Descriptions.Item>
          <Descriptions.Item label="工商登记状态">
            {data.businessRegistrationStatus}
          </Descriptions.Item>
          <Descriptions.Item label="当前状态">{data.currentStatus || '—'}</Descriptions.Item>

          <Descriptions.Item label="许可范围" span={3}>
            <div className="permit-content">{data.permittedScope}</div>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 标签页区域 */}
      <Card className="tabs-card">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'business-registration',
              label: '工商注册信息',
              children: <BusinessRegistration />,
            },
            {
              key: 'internal-management',
              label: <span>内部管理</span>,
              children: <InternalManagement />,
            },
            {
              key: 'qualification',
              label: (
                <span>
                  资质证照
                  <Badge count={1} size="small" style={{ marginLeft: 8 }} />
                </span>
              ),
              children: <Qualification />,
            },
            {
              key: 'personnel',
              label: (
                <span>
                  人员信息
                  <Badge count={1} size="small" style={{ marginLeft: 8 }} />
                </span>
              ),
              children: <Personnel />,
            },
            {
              key: 'safety',
              label: (
                <span>
                  安全生产信息
                  <Badge count={1} size="small" style={{ marginLeft: 8 }} />
                </span>
              ),
              children: <SafetyProduction />,
            },
            {
              key: 'special',
              label: '特殊作业管理',
              children: <SpecialOperation />,
            },
            {
              key: 'residents',
              label: '民爆用品',
              children: <Explosives />,
            },
            {
              key: 'foreign',
              label: '外包公司',
              children: <OutsourcingCompany />,
            },
          ]}
        />
      </Card>
    </div>
  );
};

export default BaseInfoForm;
