import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';

const { nameLengthRules, textAreaLengthRules } = RULES;

const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['行业名称', 'subName', 'Input', true],
    rules: [nameLengthRules],
  },
  {
    quickItemParams: ['行业编码', 'subCode', 'Input', false, '根据规则自动生成'],
    disabled: true,
  },
  {
    quickItemParams: ['描述', 'description', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
