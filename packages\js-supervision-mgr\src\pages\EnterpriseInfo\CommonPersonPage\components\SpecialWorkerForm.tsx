import React from 'react';
import {
  DynamicForm,
  DynamicFormItem,
  SectionTitle,
  JSCUtils,
  RULES,
  FormOpenType,
} from 'jishan-components';
import imagesImg from '@/assets/images/common/image.png';

const { setOption } = JSCUtils;
const { nameLengthRules, codeLengthRules, codeRules } = RULES;

const formItems: DynamicFormItem[] = [
  { quickItemParams: ['证书名称', 'certificateName', 'Input', true], rules: [nameLengthRules] },
  {
    quickItemParams: ['证书编号', 'certificateNo', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  {
    quickItemParams: ['作业类别', 'operationCategory', 'Select', true],
    options: [
      setOption('高处作业'),
      setOption('断路作业'),
      setOption('吊装作业'),
      setOption('盲板抽堵作业'),
      setOption('动土作业'),
      setOption('动火作业'),
      setOption('临时用电作业'),
      setOption('受限空间作业'),
      setOption('爆破作业'),
    ],
  },
  { quickItemParams: ['操作项目', 'operationItem', 'Input', true] },
  { quickItemParams: ['初领日期', 'issueDate', 'DatePicker', true] },
  {
    quickItemParams: ['有效日期', 'validDate', 'RangePicker', true],
  },
  { quickItemParams: ['应复审日期', 'reviewDueDate', 'DatePicker', true] },
  { quickItemParams: ['发证机构', 'issuingAuthority', 'Input', true] },
  { quickItemParams: ['附件', 'attachment', 'ImgUpload', false], imgInfo: { imagesImg } },
];

export const specialWorkerFormNames = formItems.map(item => item.name);

export const dateNames = ['issueDate', 'reviewDueDate', 'validDate'];

export const rangeDateNames = ['validDate'];

interface SpecialWorkerFormProps {
  cardIndex: number;
  openType: FormOpenType;
}

const SpecialWorkerForm: React.FC<SpecialWorkerFormProps> = ({ cardIndex, openType }) => {
  return (
    <>
      <SectionTitle title="人员资质信息" />
      <DynamicForm items={formItems} openType={openType} nameSuffix={cardIndex} />
    </>
  );
};

export default SpecialWorkerForm;
