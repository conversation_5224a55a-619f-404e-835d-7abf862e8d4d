import React from 'react';
import { Input, InputProps } from 'antd';

interface TdInputProps extends InputProps {
  value?: any;
  onChange?: (v: any) => void;
}

const TdInput: React.FC<TdInputProps> = ({ value, onChange, ...rest }) => {
  return (
    <td className="center bold">
      <Input {...rest} value={value} onChange={e => onChange?.(e.target.value)} />
    </td>
  );
};

export default TdInput;
