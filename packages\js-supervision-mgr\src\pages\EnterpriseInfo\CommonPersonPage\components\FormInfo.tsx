import React, { useEffect, useState } from 'react';
import { DynamicFormList, FormOpenType } from 'jishan-components';
import SpecialWorkerForm, {
  specialWorkerFormNames,
  dateNames as spDataNames,
} from './SpecialWorkerForm';
import NotSpecialWorkerForm, {
  notSpecialWorkerFormNames,
  dateNames as nsDateNames,
} from './NotSpecialWorkerForm';
import { FormInstance } from 'antd';

export const imgNames = ['attachment'];

export const dateNames = [...spDataNames, ...nsDateNames];

interface FormInfoProps {
  form?: FormInstance<any>;
  openType: FormOpenType;
  initValues?: Record<string, any>;
  cardCount?: number;
  BaseInfoForm: React.FC<any>;
}

const FormInfo: React.FC<FormInfoProps> = ({
  form,
  openType,
  initValues,
  cardCount,
  BaseInfoForm,
}) => {
  const [resetKey, setResetKey] = useState(0);
  const [isSpecialWorker, setIsSpecialWorker] = useState<string>('0');

  useEffect(() => {
    const { isSpecialWorker = '0' } = initValues || {};
    setIsSpecialWorker(isSpecialWorker);
  }, [initValues]);

  const handleSpecialWorkerChange = (e: any) => {
    const { value } = e.target || {};
    setIsSpecialWorker(value);
    setResetKey(prev => prev + 1);
    // 获取需要清空的字段名数组
    const resetFields = value === '1' ? notSpecialWorkerFormNames : specialWorkerFormNames;
    // 获取当前表单所有字段名
    const allFields = form?.getFieldsValue() || {};
    // 过滤出所有 name_number 格式的字段名
    const fieldsToReset: Record<string, any> = {};
    Object.keys(allFields).forEach(fieldName => {
      resetFields.forEach(baseName => {
        // 判断字段名是否以 baseName + '_' 开头
        if (fieldName.startsWith(`${baseName}_`)) {
          fieldsToReset[fieldName] = undefined;
        }
      });
    });
    // 清空对应的字段
    form?.setFieldsValue(fieldsToReset);
  };

  return (
    <>
      <BaseInfoForm
        openType={openType}
        form={form}
        isSpecialWorker={isSpecialWorker}
        onSpecialWorkerChange={handleSpecialWorkerChange}
      />
      <DynamicFormList
        form={form}
        openType={openType}
        resetKey={resetKey}
        FormComponent={isSpecialWorker === '1' ? SpecialWorkerForm : NotSpecialWorkerForm}
        formComponentProps={{
          openType,
        }}
        initValues={initValues}
        cardCount={cardCount}
      />
    </>
  );
};

export default FormInfo;
