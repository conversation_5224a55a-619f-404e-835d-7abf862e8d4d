import React, { useState, useEffect } from 'react';
import { DatePicker, FormInstance } from 'antd';
import '../index.less';
import dayjs from 'dayjs';
import TdTitle from './TdTitle';
import TdInput from './TdInput';

interface TdDatePickerProps {
  value?: any;
  onChange?: (v: any) => void;
  placeholder?: string;
}

const TdDatePicker: React.FC<TdDatePickerProps> = ({ value, onChange, placeholder }) => {
  return (
    <td>
      <DatePicker
        className="fullWidth"
        placeholder={placeholder}
        style={{ width: '100%' }}
        showTime={{ format: 'YYYY-MM-DD HH:mm:ss' }}
        value={value ? dayjs(value) : undefined}
        onChange={(_, dateString) => onChange?.(dateString)}
      />
    </td>
  );
};

interface DataRowProps {
  index: number;
  gasParams: any[];
  onGasParamsChange: (idx: number, key: string, value: any) => void;
}

const DataRow: React.FC<DataRowProps> = ({ index, gasParams, onGasParamsChange }) => {
  const row = gasParams[index] || {};
  const setInputE = (name: string) => {
    return {
      value: row[name],
      onChange: (v: any) => onGasParamsChange(index, name, v),
    };
  };
  return (
    <>
      <TdInput placeholder="请输入分析数据" {...setInputE('toxicGasDataOne')} />
      <TdInput {...setInputE('toxicGasDataTwo')} placeholder="请输入分析数据" />
      <TdInput {...setInputE('flammGasDataOne')} placeholder="请输入分析数据" />
      <TdInput {...setInputE('flammGasDataTwo')} placeholder="请输入分析数据" />
      <TdInput {...setInputE('oxygenContent')} placeholder="请输入氧气含量" />
      <TdDatePicker {...setInputE('sampleTime')} placeholder="选择时间" />
      <TdInput {...setInputE('samplePart')} placeholder="请输入分析部位" />
      <TdInput {...setInputE('gasPerson')} placeholder="请输入分析人" />
    </>
  );
};

interface GasAnalysisTableProps {
  form?: FormInstance<any>;
  formInitValue?: any;
}

const GasAnalysisTable: React.FC<GasAnalysisTableProps> = ({ form, formInitValue }) => {
  const [fields, setFields] = useState({
    toxicGasPjone: '',
    toxicGasPjtwo: '',
    flammGasPjone: '',
    flammGasPjtwo: '',
    toxicGasSdone: '',
    toxicGasSdtwo: '',
    flammGasSdone: '',
    flammGasSdtwo: '',
    gasParams: [{}, {}],
  });

  // 监听 formInitValue 变化，重新设置 fields
  useEffect(() => {
    if (formInitValue) {
      setFields({
        toxicGasPjone: formInitValue.toxicGasPjone,
        toxicGasPjtwo: formInitValue.toxicGasPjtwo,
        flammGasPjone: formInitValue.flammGasPjone,
        flammGasPjtwo: formInitValue.flammGasPjtwo,
        toxicGasSdone: formInitValue.toxicGasSdone,
        toxicGasSdtwo: formInitValue.toxicGasSdtwo,
        flammGasSdone: formInitValue.flammGasSdone,
        flammGasSdtwo: formInitValue.flammGasSdtwo,
        gasParams: formInitValue.gasParams || [],
      });
    }
  }, [formInitValue]);

  // 同步到 form
  useEffect(() => {
    form?.setFieldsValue(fields);
  }, [fields, form]);

  // 通用字段更新
  const handleFieldChange = (key: string, value: any) => {
    setFields(prev => ({ ...prev, [key]: value }));
  };

  // gasParams 行内字段更新
  const handleGasParamsChange = (idx: number, key: string, value: any) => {
    setFields(prev => {
      const newGasParams = [...prev.gasParams];
      newGasParams[idx] = { ...newGasParams[idx], [key]: value };
      return { ...prev, gasParams: newGasParams };
    });
  };

  const bindFieldsEvent = (name: string, type: 'gas' | 'rule') => {
    return {
      value: (fields as any)[name],
      onChange: (v: any) => handleFieldChange(name, v),
      placeholder: type === 'gas' ? '请输入气体名称' : '请输入合格标准',
    };
  };

  const { gasParams } = fields;
  return (
    <div className="gasAnalysisTableWrapper">
      <table className="gasAnalysisTable" border={1} cellPadding={8}>
        <tbody>
          {/* 第一行 */}
          <tr>
            <TdTitle title="分析气体" span={5} />
            <TdTitle title="分析项目" span={2} />
            <TdTitle title="有毒有害气体名称" colSpan={2} />
            <TdTitle title="可燃气体名称" colSpan={2} />
            <TdTitle title="氧气含量" span={2} />
            <TdTitle title="取样分析时间" span={3} />
            <TdTitle title="分析部位" span={3} />
            <TdTitle title="分析人" span={3} />
          </tr>
          {/* 第二行 */}
          <tr>
            <TdInput {...bindFieldsEvent('toxicGasPjone', 'gas')} />
            <TdInput {...bindFieldsEvent('toxicGasPjtwo', 'gas')} />
            <TdInput {...bindFieldsEvent('flammGasPjone', 'gas')} />
            <TdInput {...bindFieldsEvent('flammGasPjtwo', 'gas')} />
          </tr>
          {/* 第三行 */}
          <tr>
            <TdTitle title="合格标准" />
            <TdInput {...bindFieldsEvent('toxicGasSdone', 'rule')} />
            <TdInput {...bindFieldsEvent('toxicGasSdtwo', 'rule')} />
            <TdInput {...bindFieldsEvent('flammGasSdone', 'rule')} />
            <TdInput {...bindFieldsEvent('flammGasSdtwo', 'rule')} />
            <TdTitle title="19.5%~21%(体积分数)" />
          </tr>
          {/* 第四行 */}
          <tr>
            <TdTitle title="分析数据" span={2} />
            <DataRow index={0} gasParams={gasParams} onGasParamsChange={handleGasParamsChange} />
          </tr>
          {/* 第五行 */}
          <tr>
            <DataRow index={1} gasParams={gasParams} onGasParamsChange={handleGasParamsChange} />
          </tr>
        </tbody>
      </table>
    </div>
  );
};
export default GasAnalysisTable;
