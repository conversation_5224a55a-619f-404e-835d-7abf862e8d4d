import React from 'react';
import { CustomButton, SearchInput, DownloadDropdown, JSCUtils } from 'jishan-components';
import { baseButtonStyle } from '@/utils/constants';
import { tableColumns as tCol } from '../CommonPersonPage/constants';

const { getTemplateDownloadUrl } = JSCUtils;

export const HomeTitle = '企业从业人员管理';

export const filterItems = [
  {
    name: 'personName',
    label: '人员姓名',
    component: <SearchInput placeholder="请输入人员姓名" />,
  },
  {
    name: 'phone',
    label: '手机号码',
    component: <SearchInput placeholder="请输入手机号码" />,
  },
  {
    name: 'idCard',
    label: '身份证号',
    component: <SearchInput placeholder="请输入身份证号" />,
  },
];

const notSpecialUrl: [string, string] = [
  '非特殊作业人员模板.xlsx',
  '20250718/055e3be6-dad3-4d41-b6bb-b8120447d56d.xlsx',
];
const specialUrl: [string, string] = [
  '特殊作业人员模板.xlsx',
  '20250718/06f2a095-905e-4fa6-a4cd-5b3bacc4711e.xlsx',
];

export const customButtons: CustomButton[] = [
  { text: '新增', ...baseButtonStyle },
  { text: '删除', ...baseButtonStyle },
  { text: '导入', ...baseButtonStyle },
  {
    text: '模板下载',
    ...baseButtonStyle,
    component: (
      <DownloadDropdown
        style={baseButtonStyle}
        buttonTitle="模板下载"
        templates={[
          { label: '非特殊作业人员模板', url: getTemplateDownloadUrl(...notSpecialUrl) },
          { label: '特殊作业人员模板', url: getTemplateDownloadUrl(...specialUrl) },
        ]}
      />
    ),
  },
];

export const tableColumns: any[] = tCol;
