import React from 'react';
import { RULES, DynamicFormItem } from 'jishan-components';
import {
  companyModalSelectParams,
  defaultGenderOptions,
  educationOptions,
} from '@/utils/constants';
import { Image } from 'antd';

const { idRules, phoneLengthRules, phoneRules } = RULES;

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '企业名称', dataIndex: 'enterpriseName', width: 200 },
  { title: '人员姓名', dataIndex: 'personName', width: 120 },
  { title: '学历', dataIndex: 'education', width: 100 },
  { title: '身份证号', dataIndex: 'idCard', width: 180 },
  { title: '岗位', dataIndex: 'position', width: 150 },
  { title: '是否特殊作业人员', dataIndex: 'isSpecialWorker', width: 150 },
  {
    title: '职业资格证明',
    dataIndex: 'position_2',
    width: 180,
    render: (_value: any, row: any) => {
      // 遍历 attBatchParams，收集所有存在的 attachment
      const { attBatchParams } = row || {};
      // 过滤出有 attachment 的项，并兼容 attachment 可能为数组或单个字符串
      const images: string[] = attBatchParams
        .map((item: any) => item?.attachment)
        .filter(Boolean) // 过滤掉 undefined/null/空
        .flatMap((attachment: any) => (Array.isArray(attachment) ? attachment : [attachment]))
        .filter(Boolean); // 再次过滤掉空值
      if (images.length === 0) {
        return '--';
      }
      return (
        <Image.PreviewGroup>
          {images.map((img: string, idx: number) => (
            <span key={idx} style={{ marginRight: 5, display: 'inline-block' }}>
              <Image
                src={`${PREFIX_SERVER}/${img}`}
                width={40}
                height={40}
                style={{ objectFit: 'cover', marginRight: 8 }}
                alt="资格证明"
                preview={{ mask: '预览' }}
                fallback="https://via.placeholder.com/40x40?text=无图"
              />
            </span>
          ))}
        </Image.PreviewGroup>
      );
    },
  },
  {
    title: '有效期',
    dataIndex: 'attBatchParams_2',
    width: 180,
    render: (_value: any, row: any) => row.attBatchParams[0].validEndDate || '--',
  },
  {
    title: '应复审日期',
    dataIndex: 'attBatchParams_1',
    width: 150,
    render: (_value: any, row: any) => row.attBatchParams[0].reviewDueDate || '--',
  },
];

export const formItems: DynamicFormItem[] = [
  {
    quickItemParams: ['企业名称', 'enterpriseName', 'ModalSelect', true],
    modalSelectInfo: companyModalSelectParams,
  },
  { quickItemParams: ['作业人员姓名', 'personName', 'Input', true] },
  {
    quickItemParams: ['学历', 'education', 'Select', false],
    options: educationOptions,
  },
  {
    quickItemParams: ['性别', 'gender', 'Select', true],
    options: defaultGenderOptions,
  },
  {
    quickItemParams: ['身份证号码', 'idCard', 'Input', true],
    rules: [idRules],
  },
  {
    quickItemParams: ['手机号码', 'phone', 'Input', true],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['年龄', 'age', 'IntegerInput', false] },
  {
    quickItemParams: ['岗位', 'position', 'Select', false],
    options: [],
    // TODO 从企业岗位管理获取数据
  },
  { quickItemParams: ['职务', 'duty', 'Input', false] },
  { name: 'enterpriseCode', type: 'hideItem', relatedValue: 'enterpriseName.enterpriseCode' },
];
