import React, { useState } from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm from './components/BaseInfoForm';
import {
  HomeTitle,
  exportUrl,
  exportTitle,
  filterItems,
  tableColumns,
  customButtons,
  importUrl,
} from './constants';
import { useGeneralizedConfig } from '@/hook/useGeneralizedConfig';
import { dictConfigWrapper } from '@/utils/commonFunction';
import { deleteSource, getSourceDetail, saveSource } from '@/services/companyInfo/sourceMgr';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const SourceMgr: React.FC = () => {
  const [tableFilters, setTableFilters] = useState([...filterItems]);
  const [columns, setColumns] = useState([...tableColumns]);

  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    detailHandler,
    selectedRowsDel,
    getUploadButton,
    setFormValues,
    setExportEvent,
  } = useCommonFormPage({ HomeTitle });

  useGeneralizedConfig(setTableFilters, setColumns, {
    hazardLevel: () => dictConfigWrapper('hazard_level'),
    hazardType: () => dictConfigWrapper('hazard_type'),
  });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = () => {
    selectedRowsDel(deleteSource);
  };

  customButtons[2].component = getUploadButton(importUrl);

  customButtons[3].onClick = setExportEvent(exportUrl, exportTitle);

  // 查看详情
  const openDetail = (record: any) => {
    detailHandler(record, getSourceDetail);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getSourceDetail);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      width: 120,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => openDetail(record)}>详情</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveSource)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/sourceManagement/sourceList`}
        filterItems={[...tableFilters]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        getFormValues={setFormValues}
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default SourceMgr;
