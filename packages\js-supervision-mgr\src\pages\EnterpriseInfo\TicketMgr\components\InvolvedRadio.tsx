import React from 'react';
import { Radio } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { defaultRadioOptions } from '@/utils/constants';

const defaultValue = defaultRadioOptions.find(item => item.label === '否')?.value;

interface Props {
  value: string | undefined;
  onChange: (value: string) => void;
}

const InvolvedRadio: React.FC<Props> = ({ value, onChange }) => (
  <Radio.Group
    value={value || defaultValue}
    options={defaultRadioOptions}
    onChange={(e: RadioChangeEvent) => onChange(e.target.value)}
    defaultValue={defaultValue}
  />
);

export default InvolvedRadio;
