import React from 'react';
import { Card, Empty } from 'antd';

interface QualificationProps {
  data?: any;
}

const Qualification: React.FC<QualificationProps> = ({ data }) => {
  return (
    <div style={{ padding: '20px 0' }}>
      <Card>
        <Empty
          description="资质证照内容待开发"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    </div>
  );
};

export default Qualification;
