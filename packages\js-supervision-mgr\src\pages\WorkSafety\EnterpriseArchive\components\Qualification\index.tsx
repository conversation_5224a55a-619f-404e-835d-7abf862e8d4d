import React, { useState, useMemo } from 'react';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import { tableColumns, filterItems, mockData, getStatistics } from './constants';
import StatisticsDescription from './StatisticsDescription';
import './index.less';

interface QualificationProps {
  data?: any;
}

const Qualification: React.FC<QualificationProps> = () => {
  const [updateTrigger] = useState(0);
  const [columns, setColumns] = useState([...tableColumns]);

  // 模拟 API 请求处理
  const serviceResHandle = (_res: any) => {
    // 在实际项目中，这里会处理真实的 API 响应
    // 现在返回模拟数据
    return {
      data: mockData.data,
      totalRecords: mockData.total,
    };
  };

  // 计算统计信息
  const statistics = useMemo(() => {
    return getStatistics(mockData.data);
  }, []);

  // 描述信息组件
  const descriptionComponent = (
    <StatisticsDescription
      total={statistics.total}
      valid={statistics.valid}
      expired={statistics.expired}
    />
  );

  const openDetail = (record: any) => {
    console.log('查看', record);
  };

  const newColumns = [
    ...columns,
    {
      title: '操作',
      noTooltip: true,
      width: 100,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openDetail(record)}>查看</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <div className="qualification">
      <ListRequest
        apiUrl={`${SPPREFIX}/safetyProduct/oneCompanyOneFile/list`}
        columns={newColumns}
        filterItems={filterItems}
        updateTrigger={updateTrigger}
        showSizeChanger={true}
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
        serviceResHandle={serviceResHandle}
        inlineButtons
        tableBordered={true}
        descriptionComponent={descriptionComponent}
      />
    </div>
  );
};

export default Qualification;
