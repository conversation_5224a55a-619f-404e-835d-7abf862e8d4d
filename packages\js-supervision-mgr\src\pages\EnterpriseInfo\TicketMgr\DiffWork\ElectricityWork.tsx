import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  FrequencyItems,
  savePersonItems,
  BaseItems,
  SignItems,
  getLocationItem,
  OpinionItem,
  getLocationColumn,
} from '../constants';
import { DynamicFormItem, RULES } from 'jishan-components';
import SafetyMeasuresList from '../components/Measures';
import { checkOpinionAndName, getOpinionItems } from '../utils';
import CombustibleGas from '../components/CombustibleGas';
import { Modal } from 'antd';

const { codeRules, codeLengthRules, nameLengthRules } = RULES;

const HomeTitle = '临时用电作业';

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  getLocationColumn('作业地点'),
  { title: '电源接入点', dataIndex: 'powerAccessPoint', width: 220 },
  { title: '用电设备', dataIndex: 'equipmentName', width: 220 },
);

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.workUnit,
  FrequencyItems.applyTime,
  { quickItemParams: ['作业地点', 'workLocation', 'Input', true], rules: [nameLengthRules] },
  FrequencyItems.workContent,
  {
    quickItemParams: ['电源接入点及许可用电功率', 'powerAccessPoint', 'Input', true],
    labelCol: { span: 12 },
  },
  { quickItemParams: ['工作电压', 'workingVoltage', 'IntegerInput', true] },
  {
    quickItemParams: ['用电设备名称及额定功率', 'equipmentName', 'Input', true],
    labelCol: { span: 10 },
  },
  FrequencyItems.supervisor,
  { quickItemParams: ['用电人', 'electricityConsumer', 'Input', true] },
  {
    quickItemParams: [
      '作业人及联系方式',
      'workNamePhone',
      'Input',
      true,
      '格式：人员姓名-手机号码，如：张三-151741247',
    ],
  },
  {
    quickItemParams: ['电工证号', 'workNameElectricityNo', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  { name: 'gasResult', component: <CombustibleGas />, span: 24 },
  FrequencyItems.actualTime,
  FrequencyItems.workPlace,
  FrequencyItems.workImplementPlace,
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,

  ...OpinionItem.jobOpinion,
  ...getOpinionItems('用电单位意见', 'unit'),
  ...getOpinionItems('配送电单位意见', 'distribution'),
  ...OpinionItem.completedOpinion,

  { type: 'hideItem', name: 'sampleOneTime' },
  { type: 'hideItem', name: 'sampleTwoTime' },
  { type: 'hideItem', name: 'sampleThreeTime' },
  { type: 'hideItem', name: 'analysisOneresult' },
  { type: 'hideItem', name: 'analysisTworesult' },
  { type: 'hideItem', name: 'analysisThreeresult' },
  { type: 'hideItem', name: 'analysisPerson' },
  { type: 'hideItem', name: 'analysisPoint' },

  { type: 'hideItem', name: 'attBatchParams' },
  { type: 'hideItem', name: 'blindDrawingName' },
];

const opinionList: [string, string][] = [['unit', '用电单位意见']];

const ElectricityWork: React.FC = () => {
  const checkOpinion = (data: any) => {
    const checkRes = checkOpinionAndName(opinionList, data);
    if (typeof checkRes === 'boolean') return data;
    Modal.error({
      title: '校验未通过',
      content: (
        <div>
          {checkRes.map((err, idx) => (
            <div key={idx}>{err}</div>
          ))}
        </div>
      ),
    });
    return false;
  };

  return (
    <TicketMgr
      HomeTitle={HomeTitle}
      filterItems={[getLocationItem('作业地点'), applyTimeItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      keyName={HomeTitle}
      formItems={formItems}
      handleDataBeforeSave={checkOpinion}
      x={1600}
    />
  );
};

export default ElectricityWork;
