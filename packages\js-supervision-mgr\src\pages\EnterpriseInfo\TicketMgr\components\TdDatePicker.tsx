import React from 'react';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';

interface TdDatePickerProps {
  value?: any;
  onChange?: (v: any) => void;
  placeholder?: string;
}

const TdDatePicker: React.FC<TdDatePickerProps> = ({ value, onChange, placeholder }) => {
  return (
    <td>
      <DatePicker
        className="fullWidth"
        placeholder={placeholder}
        style={{ width: '100%' }}
        showTime={{ format: 'YYYY-MM-DD HH:mm:ss' }}
        value={value ? dayjs(value) : undefined}
        onChange={(_, dateString) => onChange?.(dateString)}
      />
    </td>
  );
};

export default TdDatePicker;
