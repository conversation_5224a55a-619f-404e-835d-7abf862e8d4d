import React from 'react';
import { Card, Empty } from 'antd';

interface OutsourcingCompanyProps {
  data?: any;
}

const OutsourcingCompany: React.FC<OutsourcingCompanyProps> = ({ data }) => {
  return (
    <div style={{ padding: '20px 0' }}>
      <Card>
        <Empty
          description="外包公司内容待开发"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    </div>
  );
};

export default OutsourcingCompany;
