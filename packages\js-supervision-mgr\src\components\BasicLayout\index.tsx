/* eslint-disable */
import { CloseOutlined } from '@ant-design/icons';
import type { MenuDataItem } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import type { TabsProps } from 'antd';
import { ConfigProvider, Dropdown, message, Tabs } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React, { useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import routes from '../../../config/routes';
import DraggableTabNode from './DraggableTabNode';
import LoadComponent from './TabsConfig';

import styles from './index.less';
import { saveMessageRead } from '@/services/systemMaintenance/alert';

const menus = [
  {
    label: '关闭其他菜单',
    key: 'other',
  },
  {
    label: '关闭左侧菜单',
    key: 'left',
  },
  {
    label: '关闭右侧菜单',
    key: 'right',
  },
];

const BasicLayout: React.FC<any> = ({ flatMenu }) => {
  const [pageTabs, setPageTabs] = useState<MenuDataItem[]>([]);
  const [closeableList, setCloseableList] = useState<boolean[]>([]);
  const [activeKey, setActiveKey] = useState('');
  const [activePath, setActivePath] = useState('');

  const [order, setOrder] = useState<React.Key[]>([]);
  const { href } = window.location;

  const dealFlatRoute = (data: any) => {
    let flatArr: any = [];
    data?.forEach((item: any) => {
      flatArr.push({
        ...item,
      });
      if (item.routes && item.routes.length > 0) {
        flatArr = [...flatArr, ...dealFlatRoute(item.routes)];
      }
    });
    return flatArr;
  };

  const flatRoutes = dealFlatRoute(routes || []);

  // 初始化标签页
  useEffect(() => {
    const list = JSON.parse(JSON.stringify(pageTabs));
    const currentRoute = window.location.pathname + window.location.hash?.split('?')?.[0];
    let urlParams = '';
    if (window.location.hash.indexOf('?') > -1) {
      urlParams = `?${window.location.hash?.split('?')?.[1]}`;
    }

    if (currentRoute === '/' || currentRoute === '/jssupervision/') {
      history.push('/companyBaseInfoMgr');
    } else {
      // if (flatMenu && flatMenu.length > 0) {

      let homeItem: any = [];
      const menuItem = flatMenu?.find((v: any) => currentRoute === `/${BASE}${v.path}`) || {};

      const tempCode = currentRoute.split('/jssupervision/')[1];
      const tempRoute = flatRoutes.find((route: any) => route.path === `/${tempCode}`);

      if (menuItem.code) {
        if (!menuItem.parentId) {
          if (tempRoute.redirect) {
            const redirectRoute = flatRoutes.find(
              (route: any) => route.path === tempRoute.redirect,
            );
            const redirectMenuItem =
              flatMenu?.find((v: any) => redirectRoute.path === v.path) || {};
            if (redirectMenuItem) {
              setActiveKey(redirectMenuItem.code);
              setActivePath(redirectMenuItem.path);
              setPageTabs([redirectMenuItem]);
            }
          } else if (menuItem.children.length === 0) {
            setActiveKey(menuItem.code);
            setActivePath(menuItem.path);
            setPageTabs([menuItem]);
          } else {
            setActiveKey('');
            setActivePath('');
            setPageTabs([]);
          }
        } else if (menuItem.children.length === 0) {
          menuItem.path = menuItem.path + urlParams;
          menuItem.code = menuItem.code + urlParams;
          setActiveKey(menuItem.code);
          setActivePath(menuItem.path);

          if (!pageTabs.find(v => v.path === menuItem.path)) {
            list.push(menuItem);
            setPageTabs([...homeItem, ...list]);
          }
        }
      } else {
        if (tempRoute) {
          const whiteList = ['/login', '/404', '/noPeri', '/column', '/columnDetail'];
          if (whiteList?.indexOf(tempRoute.path) >= 0) {
            setActiveKey(`${tempCode}${urlParams}`);
            setActivePath(tempCode);

            if (!pageTabs.find(v => v.path === `/${tempCode}${urlParams}`)) {
              list.push({
                path: `/${tempCode}${urlParams}`,
                menuName: tempRoute.name,
                key: tempCode,
                code: `${tempCode}${urlParams}`,
              });
              setPageTabs([...homeItem, ...list]);
            }
          } else {
            if (flatMenu && flatMenu.length > 0) {
              setActiveKey('noPeri');
              setActivePath('noPeri');

              if (!pageTabs.find(v => v.path === '/noPeri')) {
                list.push({
                  path: '/noPeri',
                  menuName: currentRoute.replace('/jssupervision/', ''),
                  key: 'noPeri',
                  code: 'noPeri',
                });
                setPageTabs([...homeItem, ...list]);
              }
            }
          }
        } else {
          setActiveKey('404');
          setActivePath('404');

          if (!pageTabs.find(v => v.path === '/404')) {
            list.push({
              path: '/404',
              menuName: currentRoute.replace('/jssupervision/', ''),
              key: '404',
              code: '404',
            });
            setPageTabs([...homeItem, ...list]);
          }
        }
      }
    }
  }, [href, flatMenu]);

  // 切换标签页
  const onChange = (key: string) => {
    setActiveKey(key);

    const menuItem = pageTabs.find(item => item.code === key);
    setActivePath(menuItem?.path || '');
    history.push(menuItem?.path || '/404');
  };

  const close = (e: React.MouseEvent, data: MenuDataItem, index: number) => {
    e.stopPropagation();

    let list: MenuDataItem[] = JSON.parse(JSON.stringify(pageTabs));
    // 删除当前标签页，聚焦页面往前移1
    if (activeKey === data.code) {
      const target = list[index ? index - 1 : 1];
      setActiveKey(target.code);
      setActivePath(target.path || '');

      history.push(target.path || '/404');
    }
    list = list.filter(item => item.path !== data.path);
    setPageTabs(list);
  };

  // 根据选项关闭页签
  const closeBySelection = (data: string, code: string) => {
    const homeItem = pageTabs.filter(item => item.code === '/sys_sy');
    if (data === 'other') {
      const curPage = pageTabs.find(item => item.code === code);
      if (curPage) {
        curPage.path === '/sys_sy' ? setPageTabs([curPage]) : setPageTabs([...homeItem, curPage]);
        setActiveKey(code);
        setActivePath(curPage.path || '');
      }
    }
    if (data === 'left') {
      const curPageIndex = pageTabs.findIndex(item => item.code === code);
      const list = JSON.parse(JSON.stringify(pageTabs));
      setPageTabs([...homeItem, ...list.slice(curPageIndex)]);
      setActiveKey(code);
      setActivePath(pageTabs[curPageIndex].path || '');
    }
    if (data === 'right') {
      const curPageIndex = pageTabs.findIndex(item => item.code === code);
      const list = JSON.parse(JSON.stringify(pageTabs));
      setPageTabs(list.slice(0, curPageIndex + 1));
      setActiveKey(code);
      setActivePath(pageTabs[curPageIndex].path || '');
    }
  };

  // 获取标签页
  const getTabsItems = () => {
    return pageTabs.map((item, i) => ({
      label: (
        <Dropdown
          menu={{
            items: menus.map(menu => ({
              label: (
                <div
                  onClick={() => {
                    closeBySelection(menu.key, item.code);
                  }}
                >
                  {menu.label}
                </div>
              ),
              key: menu.key,
            })),
          }}
          trigger={['contextMenu']}
        >
          <div
            className={`${styles.tabsName} ${pageTabs.length === 0 ? '' : styles.hover}`}
            onMouseEnter={e => {
              e.stopPropagation();
              const flagList = [];
              flagList[i] = true;
              setCloseableList(flagList);
            }}
            onMouseLeave={e => {
              e.stopPropagation();
              setCloseableList([]);
            }}
          >
            <span>{item.menuName}</span>
            {pageTabs.length > 1 &&
              (activeKey === item.code || closeableList[i]) &&
              item.code !== 'sys_sy' && <CloseOutlined onClick={e => close(e, item, i)} />}
          </div>
        </Dropdown>
      ),
      key: item.code,
      // 将页面作为tabpane组件内容，防止切换标签时页面刷新
      children:
        item.code === activeKey ? (
          <LoadComponent
            key={item.code}
            pageTabs={pageTabs}
            closeTagView={close}
            pageTabItem={item}
            code={item.path || `/${item.code}`}
          />
        ) : null,
    }));
  };

  function dealTabsItem() {
    let items = getTabsItems();
    const homeItem = items.filter(item => item.key === '/energyOverview');
    items = items.filter(item => item.key !== '/energyOverview');
    return [...homeItem, ...items.sort(compare)];
  }

  // 拖拽排序
  const moveTabNode = (dragKey: React.Key, hoverKey: React.Key) => {
    const newOrder = order.slice();
    const items = getTabsItems();

    items.forEach((item: any) => {
      if (item.key && newOrder.indexOf(item.key) === -1) {
        newOrder.push(item.key);
      }
    });

    const dragIndex = newOrder.indexOf(dragKey);
    const hoverIndex = newOrder.indexOf(hoverKey);

    newOrder.splice(dragIndex, 1);
    newOrder.splice(hoverIndex, 0, dragKey);

    setOrder(newOrder);
  };

  const setMesRead = async (item: any) => {
    if (item.id) {
      await saveMessageRead({ sysMessageId: item.id });
    }
  };

  // 按拖拽后的排序规则
  const compare = (a: any, b: any) => {
    const orderA = order.indexOf(a.key!);
    const orderB = order.indexOf(b.key!);
    const items = getTabsItems();

    if (orderA !== -1 && orderB !== -1) {
      return orderA - orderB;
    }
    if (orderA !== -1) {
      return -1;
    }
    if (orderB !== -1) {
      return 1;
    }

    const ia = items.indexOf(a);
    const ib = items.indexOf(b);

    return ia - ib;
  };

  useEffect(() => {
    // 拖拽排序后，修改标签页数据
    const data = pageTabs.map(item => ({
      ...item,
      key: item.code,
    }));
    if (data.length > 0) {
      setPageTabs(data.sort(compare));
    }
  }, [JSON.stringify(order)]);

  // 自定义标签页
  const renderTabBar: TabsProps['renderTabBar'] = (tabBarProps, DefaultTabBar) => (
    <DefaultTabBar className={`${styles['custom-tabs']}`} {...tabBarProps}>
      {node => (
        <DraggableTabNode key={node.key} index={node.key!} moveNode={moveTabNode}>
          {node}
        </DraggableTabNode>
      )}
    </DefaultTabBar>
  );

  const homePath = `/${BASE}/home-index`;
  const loginPath = `/${BASE}/login`;
  const columnPath = `/${BASE}/column`;
  const columnDetailPath = `/${BASE}/columnDetail`;
  const noTabsPath = [homePath, columnPath, columnDetailPath, loginPath];

  return (
    <ConfigProvider locale={zhCN}>
      <div className={styles.basicLayoutContainer} id="main-layout">
        <DndProvider backend={HTML5Backend}>
          {pageTabs.length > 0 ? (
            <Tabs
              id="tabs-id"
              renderTabBar={renderTabBar}
              activeKey={activeKey}
              type="card"
              rootClassName={noTabsPath.indexOf(window.location.pathname) >= 0 ? 'homeTab' : ''}
              onChange={onChange}
              items={dealTabsItem()}
            />
          ) : null}
        </DndProvider>
      </div>
    </ConfigProvider>
  );
};

export default BasicLayout;
