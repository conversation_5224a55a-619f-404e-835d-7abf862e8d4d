import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType } from 'jishan-components';
import { FormInstance } from 'antd';
import { fetchSelectOptions, fetchTreeSelectOptions } from '@/utils/commonFunction';
import { getIndustriesList } from '@/services/enterpriseInfo';
import { getIndustriesTreeList } from '@/services/companyInfo/industriesMgr';

const { nameLengthRules, textAreaLengthRules } = RULES;

const serviceWrapper = (name: string) => () => getIndustriesTreeList(name);

const formItems: DynamicFormItem[] = [
  { quickItemParams: ['类型名称', 'typeName', 'Input', true], rules: [nameLengthRules] },
  {
    quickItemParams: ['类型编号', 'riskType', 'Input', false, '自动生成'],
    disabled: true,
  },
  { type: 'blank' },
  { quickItemParams: ['描述', 'description', 'TextArea', true], rules: [textAreaLengthRules] },
  { type: 'hideItem', name: 'subName', relatedValue: 'subCode.label' },
  { type: 'hideItem', name: 'parentSubCode' },
];

const parentSubItem: DynamicFormItem = {
  quickItemParams: ['所属行业', 'subCode', 'Select', true],
  optionsServices: () => fetchSelectOptions(getIndustriesList, 'subName', 'subCode'),
};

const childSubItem: DynamicFormItem = {
  quickItemParams: ['所属行业', 'subCode', 'TreeSelect', true],
  treeSelectInfo: {},
};

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
  record: any;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form, record }) => {
  const newFormItems = [...formItems];
  if (record.parentId) {
    newFormItems.unshift(
      {
        quickItemParams: ['父类型名称', 'parentName', 'Input', false],
        disabled: true,
      },
      { type: 'hideItem', name: 'parentId' },
    );
    // @ts-ignore
    newFormItems[2].quickItemParams[0] = '子类型名称';
    childSubItem.treeSelectInfo = {
      treeOptionsService: () =>
        fetchTreeSelectOptions(
          serviceWrapper(record.parentSubCode),
          'subName',
          'subCode',
          undefined,
          'data',
        ),
    };
    newFormItems[4] = childSubItem;
  } else {
    // @ts-ignore
    newFormItems[0].quickItemParams[0] = '类型名称';
    newFormItems[2] = parentSubItem;
  }

  return <DynamicForm items={newFormItems} openType={openType} form={form} />;
};

export default BaseInfoForm;
