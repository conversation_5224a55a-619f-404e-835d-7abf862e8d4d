import React from 'react';
import TicketMgr from '..';
import {
  tableColumns,
  customButtons,
  applyTimeItem,
  levelItem,
  getLocationItem,
  BaseItems,
  SignItems,
  savePersonItems,
  FrequencyItems,
  defaultDateFormat,
  OpinionItem,
} from '../constants';
import { defaultOptionValueWrapper, dictConfigWrapper } from '@/utils/commonFunction';
import { DynamicFormItem, RULES } from 'jishan-components';
import SafetyMeasuresList from '../components/Measures';
import { getOpinionItems } from '../utils';

const { codeRules, codeLengthRules } = RULES;

const HomeTitle = '动火作业';

const newColumns = [...tableColumns];
newColumns.splice(
  3,
  0,
  { title: '动火地点及部位', dataIndex: 'workLocation', width: 220 },
  { title: '动火作业级别', dataIndex: 'workLevel', width: 150 },
);

const formItems: DynamicFormItem[] = [
  ...Object.values(BaseItems),
  FrequencyItems.applyTime,
  FrequencyItems.workContent,
  { quickItemParams: ['动火地点及部位', 'workLocation', 'Input', true] },
  {
    quickItemParams: ['动火作业级别', 'workLevel', 'Radio', true],
    optionsServices: () => dictConfigWrapper('work_level'),
    radioInfo: {
      onOptionsLoaded: defaultOptionValueWrapper('特级'),
    },
  },
  { quickItemParams: ['动火方式', 'fireMethod', 'Input', true] },
  {
    quickItemParams: ['动火人及证书编号', 'fireOperator', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  FrequencyItems.workUnit,
  FrequencyItems.workLeader,
  FrequencyItems.workLeaderContact,
  FrequencyItems.workStatus,
  FrequencyItems.isRiskWork,
  ...Object.values(SignItems),
  { type: 'blank' },
  {
    quickItemParams: ['气体取样分析时间', 'sampleOneTime', 'DatePicker', true],
    datePickerInfo: {
      showTime: defaultDateFormat,
    },
  },
  {
    quickItemParams: ['', 'sampleTwoTime', 'DatePicker', false, '请选择气体取样分析时间'],
    datePickerInfo: {
      showTime: defaultDateFormat,
    },
  },
  {
    quickItemParams: ['', 'sampleThreeTime', 'DatePicker', false, '请选择气体取样分析时间'],
    datePickerInfo: {
      showTime: defaultDateFormat,
    },
  },
  { quickItemParams: ['代表性气体', 'gasOnetype', 'Input', true] },
  { quickItemParams: ['', 'gasTwotype', 'Input', false, '请输入代表性气体'] },
  { quickItemParams: ['', 'gasThreetype', 'Input', false, '请输入代表性气体'] },
  { quickItemParams: ['分析结果%', 'analysisOneresult', 'Input', true] },
  { quickItemParams: ['', 'analysisTworesult', 'Input', false, '请输入分析结果'] },
  { quickItemParams: ['', 'analysisThreeresult', 'Input', false, '请输入分析结果'] },
  { quickItemParams: ['分析人', 'analysisOneperson', 'Input', true] },
  { quickItemParams: ['', 'analysisTwoperson', 'Input', false, '请输入分析人'] },
  { quickItemParams: ['', 'analysisThreeperson', 'Input', false, '请输入分析人'] },
  FrequencyItems.actualTime,
  FrequencyItems.workPlace,
  FrequencyItems.workImplementPlace,
  { name: 'measuersList', component: <SafetyMeasuresList />, span: 24 },
  ...savePersonItems,
  FrequencyItems.supervisor,
  { type: 'blank', span: 16 },
  ...OpinionItem.jobOpinion,
  ...OpinionItem.unitOpinion,
  ...OpinionItem.departmentOpinion,
  ...getOpinionItems('动火审批人意见', 'openFire'),
  ...getOpinionItems('动火前，岗位当班班长验票情况', 'ticket', { labelCol: { span: 6 } }),
  ...OpinionItem.completedOpinion,
  { type: 'hideItem', name: 'blindDrawingName' },
  { type: 'hideItem', name: 'attBatchParams' },
];

const FireWork: React.FC = () => {
  const configMap = {
    workLevel: () => dictConfigWrapper('work_level'),
  };

  return (
    <TicketMgr
      keyName="动火"
      HomeTitle={HomeTitle}
      filterItems={[getLocationItem('动火地点及部位'), applyTimeItem, levelItem]}
      customButtons={customButtons}
      tableColumns={newColumns}
      configMap={configMap}
      formItems={formItems}
    />
  );
};

export default FireWork;
