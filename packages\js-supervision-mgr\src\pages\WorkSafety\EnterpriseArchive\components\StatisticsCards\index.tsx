import React, { useState, useEffect } from 'react';
import { Row, Col, message } from 'antd';
import { HomeOutlined, FileTextOutlined, TeamOutlined, AlertOutlined } from '@ant-design/icons';
import { getCompanyStatistics } from '@/services/workSafety/enterpriseArchive';
import './index.less';

export interface StatisticItem {
  title: string;
  count: number;
  isWarning?: boolean; // 是否为警告状态（红色数字）
}

export interface StatisticCardData {
  id: string;
  icon: React.ReactNode;
  items: StatisticItem[];
}

export interface StatisticsCardsProps {
  gutter?: number | [number, number];
  className?: string;
  onCardClick?: (cardId: string, item: StatisticItem) => void;
}

// 默认配置数据 - 4组卡片，每组包含两个维度的数据
const defaultData: StatisticCardData[] = [
  {
    id: 'enterprise-group',
    icon: <HomeOutlined />,
    items: [
      { title: '企业数量', count: 0 },
      { title: '外包人员数量', count: 0 },
    ],
  },
  {
    id: 'certificate-group',
    icon: <FileTextOutlined />,
    items: [
      { title: '特种作业人员数量', count: 0 },
      { title: '有效证书数量', count: 0 },
    ],
  },
  {
    id: 'personnel-group',
    icon: <TeamOutlined />,
    items: [
      { title: '无效证书数量', count: 0, isWarning: false },
      { title: '复审超期数量', count: 0, isWarning: false },
    ],
  },
  {
    id: 'equipment-group',
    icon: <AlertOutlined />,
    items: [{ title: '设备实时监测预警', count: 0, isWarning: false }],
  },
];

const StatisticsCards: React.FC<StatisticsCardsProps> = ({
  gutter = [16, 16],
  className = '',
  onCardClick,
}) => {
  const [statisticsData, setStatisticsData] = useState<StatisticCardData[]>([]);

  // 转换接口数据为组件所需格式
  const transformStatisticsData = (apiData: any): StatisticCardData[] => {
    return [
      {
        id: 'enterprise-group',
        icon: <HomeOutlined />,
        items: [
          { title: '企业数量', count: parseInt(apiData.totalOutsourcingCompanyCount, 10) || 0 },
          {
            title: '工商登记状态异常',
            count: parseInt(apiData.totalOutsourcingPersonCount, 10) || 0,
          },
        ],
      },
      {
        id: 'certificate-group',
        icon: <FileTextOutlined />,
        items: [
          { title: '企业资质证照数量', count: parseInt(apiData.totalSpecialWorkerCount, 10) || 0 },
          {
            title: '超期证照数量',
            count: parseInt(apiData.totalSpecialWorkerValidCertCount, 10) || 0,
          },
        ],
      },
      {
        id: 'personnel-group',
        icon: <TeamOutlined />,
        items: [
          {
            title: '特种作业人员数量',
            count: parseInt(apiData.totalSpecialWorkerInvalidCertCount, 10) || 0,
            isWarning: (parseInt(apiData.totalSpecialWorkerInvalidCertCount, 10) || 0) > 0,
          },
          {
            title: '无效证书数量',
            count: parseInt(apiData.totalSpecialWorkerReviewOverdueCount, 10) || 0,
            isWarning: (parseInt(apiData.totalSpecialWorkerReviewOverdueCount, 10) || 0) > 0,
          },
        ],
      },
      {
        id: 'equipment-group',
        icon: <AlertOutlined />,
        items: [
          {
            title: '设备实时监测预警',
            count: 0, // 接口暂无此数据
            isWarning: false,
          },
        ],
      },
    ];
  };

  // 获取统计数据
  const fetchStatisticsData = async () => {
    try {
      const response = await getCompanyStatistics();
      if (response.code === '200' && response.data) {
        const transformedData = transformStatisticsData(response.data);
        setStatisticsData(transformedData);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  };

  // 页面加载时获取统计数据
  useEffect(() => {
    fetchStatisticsData();
  }, []);

  const handleItemClick = (cardId: string, item: StatisticItem) => {
    if (onCardClick) {
      onCardClick(cardId, item);
    }
  };

  // 每组卡片占6列（24/4=6），实现4列布局
  const span = 6;

  return (
    <div className={`enterprise-statistics-cards ${className}`}>
      <Row gutter={gutter}>
        {statisticsData.map((card: StatisticCardData) => (
          <Col span={span} key={card.id}>
            <div className="enterprise-statistics-card" data-card-id={card.id}>
              <div className="enterprise-statistics-card-icon">{card.icon}</div>
              <div className="enterprise-statistics-card-content">
                {card.items.map((item: StatisticItem, index: number) => (
                  <div
                    key={index}
                    className="enterprise-statistics-item"
                    onClick={() => handleItemClick(card.id, item)}
                  >
                    <div
                      className={`enterprise-statistics-count ${item.isWarning ? 'warning' : ''}`}
                    >
                      {item.count}
                    </div>
                    <div className="enterprise-statistics-title">{item.title}</div>
                  </div>
                ))}
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default StatisticsCards;
