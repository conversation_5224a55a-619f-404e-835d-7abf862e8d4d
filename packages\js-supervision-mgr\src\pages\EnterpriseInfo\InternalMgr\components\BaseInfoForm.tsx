import React from 'react';
import {
  DynamicForm,
  DynamicFormItem,
  buildRules,
  RULES,
  FormOpenType,
  ImageExample,
} from 'jishan-components';
import imagesImg from '@/assets/images/common/image.png';
import { fetchSelectOptions } from '@/utils/commonFunction';
import { getCompanyList } from '@/services/enterpriseInfo';
import { FormInstance } from 'antd';

const { nameLengthRules, phoneRules, phoneLengthRules } = RULES;

const formItems: DynamicFormItem[] = [
  {
    label: '企业名称',
    name: 'enterpriseCode',
    type: 'Select',
    rules: buildRules('请输入企业名称', true, nameLengthRules),
    optionsServices: () => fetchSelectOptions(getCompanyList, 'enterpriseName', 'enterpriseCode'),
  },
  {
    label: '人员姓名',
    name: 'personName',
    rules: buildRules('人员姓名', true, nameLengthRules),
  },
  {
    label: '职务',
    name: 'position',
    rules: buildRules('职务', false),
  },
  {
    label: '电话',
    name: 'phone',
    rules: buildRules('电话', false, phoneRules, phoneLengthRules),
  },
  {
    label: '任命文件附件',
    name: 'appointmentFile',
    type: 'ImgUpload',
    imgInfo: { imagesImg },
    extraContent: (
      <ImageExample
        containerStyle={{ position: 'absolute', top: 0, right: 30 }}
        src={`${PREFIX_SERVER}/api/downloadFile?bucket=portal&fileName=111.jpg&objectName=20250707/9d5713cd-dff5-40e4-b464-56ff7ef38fe2.jpg`}
      />
    ),
  },
  {
    label: '资质证件附件',
    name: 'qualificationFiles',
    type: 'FileUpload',
    rules: buildRules('资质证件附件', false),
    uploadFileInfo: {
      tip: '支持拓展名：.pdf、.png、.jpg、.jpeg...',
    },
  },
  {
    name: 'enterpriseName',
    type: 'hideItem',
    relatedValue: 'enterpriseCode.label',
  },
];

export const BaseInfoFormNames = formItems.map(item => item.name);

export const imgNames = ['appointmentFile'];

export const fileNames = ['qualificationFiles'];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

// TODO 冶金用户的差分处理
const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
