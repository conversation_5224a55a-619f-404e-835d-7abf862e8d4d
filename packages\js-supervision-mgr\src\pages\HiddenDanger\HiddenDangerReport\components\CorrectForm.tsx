import React from 'react';
import { DynamicForm, DynamicFormItem, FormContainer } from 'jishan-components';
import imagesImg from '@/assets/images/common/image.png';
import { message } from 'antd';
import { saveReport } from '@/services/hiddenDanger/dangerReport';

export const formItems: DynamicFormItem[] = [
  { quickItemParams: ['整改时间', 'correctionTime', 'DatePicker', true] },
  { quickItemParams: ['整改人', 'correctionPerson', 'Input', true] },
  { quickItemParams: ['整改内容', 'correctionContent', 'TextArea', false] },
  {
    quickItemParams: ['整改后照片', 'afterPhotos', 'ImgUpload', false],
    imgInfo: { imagesImg },
  },
];

export const CorrectContainer: React.FC<{ record: any; onClose: any }> = ({ record, onClose }) => {
  const saveHandler = async (formData: any) => {
    const newFormData = { ...formData };
    newFormData.id = record.id;

    const res = await saveReport(newFormData);
    if (res.code === '200') {
      message.success('保存成功！');
      onClose();
      return true;
    }
    message.error(res.msg);
    return false;
  };

  return (
    <FormContainer
      onClose={onClose}
      onSubmit={saveHandler}
      formFieldOptions={{
        normalFileNames: ['afterPhotos'],
        fileStringFormat: true,
      }}
      visible
      formComp={<DynamicForm items={formItems} openType="add" />}
    />
  );
};
