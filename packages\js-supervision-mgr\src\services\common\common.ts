// @ts-ignore
/* eslint-disable */
import request from 'umi-request';
import { request as netRequest } from '@/utils/net';

/** 查询天气 */
export async function getWeatherInfoByCityCode(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/gaoDe/getWeatherInfoByCityCode`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询字典表数据 */
export async function getDictByCode(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/api/sysDict/list`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询企业列表 */
export async function queryEnterpriseList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/api/enterprise/list`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 图片预览
export async function imgAndVideoViewer(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/file/imgAndVideoViewer`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

export async function sysDictList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/api/sysDict/list`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

export async function qryAreaTree() {
  return netRequest.get(`${SPPREFIX}/basicinformation/enterpriseInformation/qryAreaTree`);
}
