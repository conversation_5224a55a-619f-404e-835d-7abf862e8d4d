// RankingChart.tsx
import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';

interface RankingChartProps {
  rankData: any[];
}

const RankingChart: React.FC<RankingChartProps> = ({ rankData }) => {
  const rankingChartRef = useRef<HTMLDivElement>(null);
  const [rankingData, setRankingData] = useState<number[]>([]);
  const [rankingCategories, setRankingCategories] = useState<string[]>([]);

  useEffect(() => {
    if (Array.isArray(rankData)) {
      const sortedRankData = [...rankData].sort(
        (a, b) => parseInt(b.totalCount, 10) - parseInt(a.totalCount, 10),
      );
      setRankingData(sortedRankData.map(region => parseInt(region.totalCount, 10)));
      setRankingCategories(sortedRankData.map(region => region.dictValue));
    }
  }, [rankData]);

  useEffect(() => {
    if (rankingChartRef.current && rankingData.length > 0 && rankingCategories.length > 0) {
      const rankingChart = echarts.init(rankingChartRef.current);
      const rankingOption = {
        title: {
          text: '预警信息排名',
          left: 'center',
        },
        grid: {
          left: 50,
          right: 40,
          top: 20,
          bottom: 20,
        },
        xAxis: {
          show: false,
        },
        yAxis: {
          type: 'category',
          data: rankingCategories,
          inverse: true,
          axisLabel: {
            show: true,
            align: 'right',
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            data: rankingData,
            type: 'bar',
            barWidth: '20%',
            barCategoryGap: '10',
            itemStyle: {
              color: (params: any) => {
                const colors = ['#FF0000', '#FFA500', '#FFFF00']; // 红、橙、黄
                return params.dataIndex < 3 ? colors[params.dataIndex] : '#D3D3D3'; // 淡灰色
              },
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}',
            },
          },
        ],
      };
      rankingChart.setOption(rankingOption);
    }
  }, [rankingData, rankingCategories]);
  return <div ref={rankingChartRef} style={{ width: '100%', height: '100%' }} />;
};
export default RankingChart;
