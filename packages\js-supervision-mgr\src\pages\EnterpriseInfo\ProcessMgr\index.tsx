import React from 'react';
import CommonPage from '@/components/CommonPage';
import { ListRequest, CommonLinkButton } from 'jishan-components';
import BaseInfoForm, { fileNames, imgNames } from './components/BaseInfoForm';
import { tableColumns, HomeTitle, filterItems, customButtons } from './constants';
import { deleteProcess, getProcessDeatil, saveProcess } from '@/services/companyInfo/processMgr';
import { useCommonFormPage } from '@/hook/useCommonFormPage';

const ProcessMgr: React.FC = () => {
  const {
    updateTrigger,
    openType,
    initValue,
    formTitle,
    formVisible,
    formLoading,
    openFormWithAdd,
    onRowSelect,
    turnToListPage,
    saveHandler,
    editHandler,
    selectedRowsDel,
  } = useCommonFormPage({ HomeTitle });

  customButtons[0].onClick = openFormWithAdd;

  customButtons[1].onClick = async () => {
    selectedRowsDel(deleteProcess);
  };

  // 编辑
  const openEdit = (record: any) => {
    editHandler(record, getProcessDeatil);
  };

  const newColumns = [
    ...tableColumns,
    {
      title: '操作',
      noTooltip: true,
      fixed: 'right',
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => openEdit(record)}>编辑</CommonLinkButton>
        </div>
      ),
    },
  ];

  return (
    <CommonPage
      title={formTitle}
      backIconClick={turnToListPage}
      openType={openType}
      initialValues={initValue}
      formVisible={formVisible}
      onSubmit={async (formData: any) => saveHandler(formData, saveProcess)}
      formComp={<BaseInfoForm openType={openType} />}
      onClose={turnToListPage}
      formFieldOptions={{
        normalFileNames: fileNames,
        imageFieldNames: imgNames,
        fileStringFormat: true,
        imgStringFormat: true,
      }}
      formLoading={formLoading}
    >
      <ListRequest
        apiUrl={`${SPPREFIX}/basicinformation/productionProcess/processList`}
        filterItems={[...filterItems]}
        updateTrigger={updateTrigger}
        columns={newColumns}
        rowSelection
        onRowSelect={onRowSelect}
        customButtons={customButtons}
        inlineButtons
        buttonCol={1}
        labelCol={{ span: 8 }}
        scrollYDelta={48}
      />
    </CommonPage>
  );
};

export default ProcessMgr;
