import React from 'react';
import { SearchInput, CommonLinkButton } from 'jishan-components';

// 表格列定义
export const tableColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
  },
  {
    title: '人员姓名',
    dataIndex: 'personName',
    width: 120,
  },
  {
    title: '职务',
    dataIndex: 'position',
    width: 150,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 150,
  },
  {
    title: '任命文件',
    dataIndex: 'appointmentFile',
    render: (_text: any, record: any) => (
      <div>
        <CommonLinkButton
          onClick={() => {
            if (_text) {
              // 这里可以添加文件预览或下载逻辑
              console.log('打开文件:', _text);
            }
          }}
        >
          {_text || '—'}
        </CommonLinkButton>
      </div>
    ),
  },
  {
    title: '资质证件',
    dataIndex: 'qualificationFile',
    render: (_text: any, record: any) => (
      <div>
        <CommonLinkButton
          onClick={() => {
            if (_text) {
              // 这里可以添加文件预览或下载逻辑
              console.log('打开文件:', _text);
            }
          }}
        >
          {_text || '—'}
        </CommonLinkButton>
      </div>
    ),
  },
];

// 筛选条件
export const filterItems = [
  {
    name: 'enterpriseName',
    label: '人员姓名',
    span: 4,
    component: <SearchInput placeholder="请输入人员姓名" />,
  },
];

// 模拟数据
export const mockData = {
  data: [
    {
      id: 1,
      personName: '李明',
      position: '机构负责人',
      phone: '13412345567',
      appointmentFile: '任命书_李明.jpg',
      qualificationFile: '注册安全工程师执业资格证书.jpg',
    },
    {
      id: 2,
      personName: '张华',
      position: '安全专员',
      phone: '13998809876',
      appointmentFile: '任命书_张华.jpg',
      qualificationFile: '注册安全工程师执业资格证书.jpg',
    },
    {
      id: 3,
      personName: '王小利',
      position: '安全培训师',
      phone: '13042400999',
      appointmentFile: '任命书_王小利.jpg',
      qualificationFile: '注册安全工程师执业资格证书.jpg',
    },
    {
      id: 4,
      personName: '赵柯',
      position: '安全监督员',
      phone: '19908756432',
      appointmentFile: '任命书_赵柯.jpg',
      qualificationFile: '注册安全工程师执业资格证书.jpg',
    },
    {
      id: 5,
      personName: '李红',
      position: '设备安检员',
      phone: '13289765432',
      appointmentFile: '任命书_李红.jpg',
      qualificationFile: '注册安全工程师执业资格证书.jpg',
    },
    {
      id: 6,
      personName: '田家亮',
      position: '应急处理专家',
      phone: '15612345678',
      appointmentFile: '任命书_田家亮.jpg',
      qualificationFile: '注册安全工程师执业资格证书.jpg',
    },
  ],
  total: 6,
  pageNo: 1,
  pageSize: 10,
};
