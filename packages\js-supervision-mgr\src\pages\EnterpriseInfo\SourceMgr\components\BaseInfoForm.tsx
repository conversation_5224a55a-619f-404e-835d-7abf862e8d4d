import React from 'react';
import { DynamicForm, DynamicFormItem, RULES, FormOpenType, buildRules } from 'jishan-components';
import { FormInstance } from 'antd';
import { sysDictList } from '@/services/common/common';
import { companyModalSelectParams } from '@/utils/constants';

const {
  nameLengthRules,
  codeLengthRules,
  phoneLengthRules,
  phoneRules,
  codeRules,
  textAreaLengthRules,
} = RULES;

const centerLabelStyles: any = {
  whiteSpace: 'normal',
  wordBreak: 'break-all',
  minHeight: 66,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

const formItems: DynamicFormItem[] = [
  {
    label: '企业',
    name: 'enterpriseName',
    type: 'ModalSelect',
    rules: buildRules('请选择企业', true, nameLengthRules),
    modalSelectInfo: companyModalSelectParams,
  },
  {
    quickItemParams: ['危险源编码', 'hazardCode', 'Input', true],
    rules: [codeRules, codeLengthRules],
  },
  { quickItemParams: ['危险源名称', 'hazardName', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['危险源地址', 'hazardAddress', 'Input', true], rules: [nameLengthRules] },
  { quickItemParams: ['压力kpa', 'pressureKpa', 'Input', false] },
  { quickItemParams: ['设计液位mm', 'designLevelMm', 'Input', false] },
  { quickItemParams: ['温度', 'temperature', 'Input', false] },
  { quickItemParams: ['有毒可燃气体浓度mol/L', 'toxicGasConcentration', 'Input', false] },
  { quickItemParams: ['联系人', 'contactPerson', 'Input', false] },
  {
    quickItemParams: ['联系电话', 'contactPhone', 'Input', false],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['管控措施', 'controlMeasures', 'Input', false] },
  { quickItemParams: ['应急措施', 'emergencyMeasures', 'Input', false] },
  { quickItemParams: ['投用时间', 'putIntoUseDate', 'DatePicker', false] },
  {
    quickItemParams: ['危险源等级', 'hazardLevel', 'Select', true],
    optionsServices: async () => {
      const { data } = await sysDictList({ classCodes: 'hazard_level' });
      return data.map((item: any) => ({ label: item.dictValue, value: item.dictCode }));
    },
  },
  { type: 'blank' },
  {
    quickItemParams: [
      '重大危险源与周边重点防护目标最近距离情况(m)',
      'nearestDistance',
      'FloatInput',
      false,
    ],
    customLabel: <div style={centerLabelStyles}>重大危险源与周边重点防护目标最近距离情况(m)</div>,
    flexLabel: true,
  },
  {
    quickItemParams: ['厂区边界外500m范围内人数估算值', 'peopleAround', 'IntegerInput', false],
    customLabel: <div style={centerLabelStyles}>厂区边界外500m范围内人数估算值</div>,
    flexLabel: true,
  },
  {
    quickItemParams: ['近三年内危险化学品事故情况', 'accidentHistory', 'Input', false],
    customLabel: <div style={centerLabelStyles}>近三年内危险化学品事故情况</div>,
    flexLabel: true,
  },
  { quickItemParams: ['R值', 'rValue', 'Input', true] },
  { quickItemParams: ['主要装置设施名称和数量', 'mainFacilities', 'Input', false] },
  { quickItemParams: ['重大危险源辨识结论', 'identificationConclusion', 'Input', false] },
  { quickItemParams: ['重大危险源分级依据', 'classificationBasis', 'Input', false] },
  { quickItemParams: ['风险值', 'riskValue', 'Input', false] },
  {
    quickItemParams: ['危险源类型', 'hazardType', 'Select', true],
    optionsServices: async () => {
      const { data } = await sysDictList({ classCodes: 'hazard_type' });
      return data.map((item: any) => ({ label: item.dictValue, value: item.dictCode }));
    },
  },
  {
    quickItemParams: ['边界经纬度集合json', 'boundaryCoordinates', 'TextArea', false],
    rules: [textAreaLengthRules],
  },
  { quickItemParams: ['区域位置图', 'locationMap', 'Input', false] },
  { quickItemParams: ['主要负责人', 'chiefPerson', 'Input', false] },
  {
    quickItemParams: ['主要负责人联系方式', 'chiefPersonPhone', 'Input', false],
    rules: [phoneRules, phoneLengthRules],
  },
  {
    quickItemParams: ['主要负责人职务', 'chiefPersonTitle', 'Input', false],
    rules: [nameLengthRules],
  },
  { quickItemParams: ['技术负责人', 'techPerson', 'Input', false] },
  {
    quickItemParams: ['技术负责人联系方式', 'techPersonPhone', 'Input', false],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['技术负责人职务', 'techPersonTitle', 'Input', false] },
  { quickItemParams: ['操作负责人', 'operationPerson', 'Input', false] },
  {
    quickItemParams: ['操作负责人联系方式', 'operationPersonPhone', 'Input', false],
    rules: [phoneRules, phoneLengthRules],
  },
  { quickItemParams: ['操作负责人职务', 'operationPersonTitle', 'Input', false] },
  { quickItemParams: ['审核状态', 'auditStatus', 'Input', false] },
  { name: 'enterpriseCode', type: 'hideItem', relatedValue: 'enterpriseName.enterpriseCode' },
];

export const fileNames = [
  'establishmentApprovalAttach',
  'trialSupervisionAttach',
  'completionInspectionAttach',
];

interface BaseInfoFormProps {
  openType: FormOpenType;
  form?: FormInstance<any>;
}

const BaseInfoForm: React.FC<BaseInfoFormProps> = ({ openType, form }) => {
  return (
    <>
      <DynamicForm items={formItems} openType={openType} form={form} />
    </>
  );
};

export default BaseInfoForm;
